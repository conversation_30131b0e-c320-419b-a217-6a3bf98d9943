@echo off
setlocal

:: Define source and destination directories
set "source=\\10.16.2.207\department$\Qsystem"
set "destination=C:\xampp\htdocs\wamnextgen\uploads\reservations"
set "interval=60"  :: Interval in seconds

:: Infinite loop
:loop
    :: Check if source directory exists
    if not exist "%source%" (
        echo Source directory "%source%" does not exist.
        goto :eof
    )

    :: Check if destination directory exists, if not create it
    if not exist "%destination%" (
        echo Destination directory "%destination%" does not exist. Creating it now.
        mkdir "%destination%"
    )

    :: Use robocopy to sync files
    echo Synchronizing files from "%source%" to "%destination%"...
    robocopy "%source%" "%destination%" /MIR /R:3 /W:5

    :: Wait for the specified interval before checking again
    echo Waiting for %interval% seconds...
    timeout /t %interval% /nobreak >nul

    :: Repeat the loop
    goto :loop