@echo off
setlocal enabledelayedexpansion

:: Define source and destination directories
set "source=\\***********\department$\Qsystem"
set "destination=C:\xampp\htdocs\wamnextgen\uploads\reservations"
set "interval=60"  :: Interval in seconds

:: URLs to call when files change (modify these URLs as needed)
set "url1=http://localhost/wamnextgen/api/file_changed"
set "url2=http://localhost/wamnextgen/cron/process_files"

:: File to store the last sync timestamp
set "timestamp_file=%destination%\.last_sync_timestamp"

:: Create a temporary file to capture robocopy output
set "temp_log=%temp%\robocopy_sync.log"

:: Infinite loop
:loop
    :: Check if source directory exists
    if not exist "%source%" (
        echo [%date% %time%] Source directory "%source%" does not exist.
        goto :eof
    )

    :: Check if destination directory exists, if not create it
    if not exist "%destination%" (
        echo [%date% %time%] Destination directory "%destination%" does not exist. Creating it now.
        mkdir "%destination%"
    )

    :: Use robocopy to sync files and capture output
    echo [%date% %time%] Synchronizing files from "%source%" to "%destination%"...
    robocopy "%source%" "%destination%" /MIR /R:3 /W:5 /LOG:"%temp_log%" /TEE

    :: Check if robocopy found any changes (exit codes 1, 2, 3 indicate files were copied)
    set "robocopy_exit_code=%errorlevel%"

    :: Robocopy exit codes:
    :: 0 = No files were copied (no change)
    :: 1 = Files were copied successfully (changes detected)
    :: 2 = Extra files or directories were detected (changes detected)
    :: 3 = Files were copied and extra files/directories were detected (changes detected)
    :: 4+ = Errors occurred

    if !robocopy_exit_code! geq 1 if !robocopy_exit_code! leq 3 (
        echo [%date% %time%] File changes detected! Calling notification URLs...

        :: Update timestamp file
        echo %date% %time% > "%timestamp_file%"

        :: Call first URL
        echo [%date% %time%] Calling URL 1: %url1%
        powershell -Command "try { Invoke-WebRequest -Uri '%url1%' -Method GET -TimeoutSec 30 | Out-Null; Write-Host 'URL 1 called successfully' } catch { Write-Host 'Error calling URL 1:' $_.Exception.Message }"

        :: Call second URL
        echo [%date% %time%] Calling URL 2: %url2%
        powershell -Command "try { Invoke-WebRequest -Uri '%url2%' -Method GET -TimeoutSec 30 | Out-Null; Write-Host 'URL 2 called successfully' } catch { Write-Host 'Error calling URL 2:' $_.Exception.Message }"

        echo [%date% %time%] Notification URLs called.
    ) else if !robocopy_exit_code! equ 0 (
        echo [%date% %time%] No file changes detected.
    ) else (
        echo [%date% %time%] Robocopy encountered errors (exit code: !robocopy_exit_code!^)
    )

    :: Clean up temporary log file
    if exist "%temp_log%" del "%temp_log%"

    :: Wait for the specified interval before checking again
    echo [%date% %time%] Waiting for %interval% seconds...
    timeout /t %interval% /nobreak >nul

    :: Repeat the loop
    goto :loop