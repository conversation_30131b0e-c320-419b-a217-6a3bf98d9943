<!DOCTYPE html>
<html class="" lang="en">
    <head>
        <meta http-equiv="X-UA-Compatible" content="IE=9; IE=8; IE=EDGE" />
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0">
        <meta name="description" content="admin-themes-lab">
        <meta name="author" content="themes-lab">
        <link rel="shortcut icon" href="<?php echo base_url(); ?>assets/images/favicon.png" type="image/png">
        <title><?php echo CI_get_instituation_name::get_instituation_name_logo()->OrganisationEnglishName; //class defined in core-->MY_controller                                                                                                         ?></title>
        <link href="<?php echo base_url()?>assets/css/fonts/nothing_you_could_do.css" rel="stylesheet" type="text/css">
        <link href="<?php echo base_url(); ?>assets/css/style.css" rel="stylesheet"> <!-- MANDATORY -->
        <link href="<?php echo base_url(); ?>assets/css/theme.css" rel="stylesheet"> <!-- MANDATORY -->
        <link href="<?php echo base_url(); ?>assets/css/ui.css" rel="stylesheet"> <!-- MANDATORY -->
        <script src="<?php echo base_url(); ?>assets/plugins/jquery/jquery-1.11.1.min.js"></script>
        <style>
			.btn {
				white-space: normal!important;
				font-weight: bold;
			}
            .left{
                float: left;
            }
            .right{
                float: right;
            }
            .center{
                margin: auto;
                display:block;
            }
            .panel{
                border: none;
                webkit-box-shadow: none;
                box-shadow: none;
            }
            .m_t_100 {
                margin-top: 5%;
                text-align: center;
            }
            /*            #next_button{
                            float:right;
                        }
                        #back_button{
                            float:left;
                        }*/
            #window_no{
                width:40%;
                text-align: center;
                margin-left: 400px;

            }
            .switch {
                background-color: #fff;
                box-shadow: inset 0 -1px #ffffff, inset 0 1px 1px rgba(0, 0, 0, 0.05);
                cursor: pointer;
                display: inline-block;
                height: 20px;
                margin-top: 5px;
                padding: 3px;
                position: relative;
                vertical-align: top;
                width: 56px;
                -webkit-border-radius: 18px;
                -moz-border-radius: 18px;
                border-radius: 18px;
            }
            .switch-green > .switch-input:checked ~ .switch-label {
                background: #4fb845;
            }
            .switch-input:checked ~ .switch-label {
                background: #319DB5;
                text-align: left;
                box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15), inset 0 0 3px rgba(0, 0, 0, 0.2);
            }
            .switch-label {
                background: #eceeef;
                border-radius: inherit;
                box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.12), inset 0 0 2px rgba(0, 0, 0, 0.15);
                display: block;
                font-size: 10px;
                height: inherit;
                position: relative;
                text-transform: uppercase;
                -webkit-transition: all 0.15s ease-out;
                -moz-transition: all 0.15s ease-out;
                -o-transition: all 0.15s ease-out;
                -ms-transition: all 0.15s ease-out;
                transition: all 0.15s ease-out;
            }
            .switch-handle {
                background: #fff;
                background-image: -moz-linear-gradient(top, #ffffff 40%, #f0f0f0);
                background-image: -o-linear-gradient(top, #ffffff 40%, #f0f0f0);
                background-image: -webkit-linear-gradient(top, #ffffff 40%, #f0f0f0);
                background-image: linear-gradient(to bottom, #ffffff 40%, #f0f0f0);
                border-radius: 10px;
                box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.2);
                height: 18px;
                left: 4px;
                position: absolute;
                top: 4px;
                width: 18px;
                -webkit-transition: left 0.15s ease-out;
                -moz-transition: left 0.15s ease-out;
                -o-transition: left 0.15s ease-out;
                -ms-transition: left 0.15s ease-out;
                transition: left 0.15s ease-out;
            }
            .m-r-20 {
                margin-right: 20px !important;
            }
            label {
                position:inherit;
                display: inline-block;
                max-width: 100%;
                margin-bottom: 5px;
                font-color: #fff;
                font-size: 20px;
                font-weight: 700;
            }
            .lb_buttons {
                <?php echo $data->lb_font_size != '' ? 'font-size: ' . $data->lb_font_size . 'px !important' : ''?>;
                <?php echo $data->lb_offset_y != '' ? 'margin-top: ' . $data->lb_offset_y . 'px !important' : ''?>;
                <?php echo $data->lb_font != '' ? 'font-family: ' . $data->lb_font : ''?>;
                <?php echo $data->lb_font_color != '' ? 'color: ' . $data->lb_font_color : ''?>;
            }
            .lb_buttons_offset_x {
                <?php echo $data->lb_offset_x != '' ? 'margin-left: ' . $data->lb_offset_x . 'px !important' : ''?>;
            }
            .lb_space {
                <?php echo $data->lb_space != '' ? 'margin-left: ' . $data->lb_space . 'px !important' : ''?>;
            }
            .sb_buttons {
                <?php echo $data->sb_width != '' ? 'width: ' . $data->sb_width . 'px !important' : ''?>;
                <?php echo $data->sb_height != '' ? 'height: ' . $data->sb_height . 'px !important' : ''?>;
                <?php echo $data->sb_font_size != '' ? 'font-size: ' . $data->sb_font_size . 'px !important' : ''?>;
                <?php echo $data->sb_offset_y != '' ? 'margin-top: ' . $data->sb_offset_y . 'px !important' : ''?>;
                <?php echo $data->sb_font != '' ? 'font-family: ' . $data->sb_font : ''?>;
                <?php echo $data->sb_font_color != '' ? 'color: ' . $data->sb_font_color : ''?>;
                <?php echo $data->sb_border_size != '' ? 'border: ' . $data->sb_border_size . 'px solid ' . $data->sb_border_color . ' !important' : ''?>;
            }
            .header_text{
                <?php echo $data->text_header_color != '' ? 'color: ' . $data->text_header_color : ''?>;
                <?php echo $data->hdr_font_size != '' ? 'font-size: ' . $data->hdr_font_size . 'px !important' : ''?>;
                <?php echo $data->hdr_font != '' ? 'font-family: ' . $data->hdr_font : ''?>;
            }
            .modal-content,.form-control:focus {
                <?php echo $data->agb_width != '' ? 'width: ' . $data->agb_width . 'px !important' : ''?>;
                <?php echo $data->agb_height != '' ? 'height: ' . $data->agb_height . 'px !important' : ''?>;
                <?php echo $data->agb_offset_y != '' ? 'margin-top: ' . $data->agb_offset_y . 'px !important' : ''?>;
                <?php echo $data->agb_border_size != '' ? 'border: ' . $data->agb_border_size . 'px solid ' . $data->agb_border_color . ' !important' : ''?>;
                <?php echo $data->agb_font != '' ? 'font-family: ' . $data->sb_font : ''?>;
                <?php echo $data->agb_font_color != '' ? 'color: ' . $data->agb_font_color : ''?>;
                <?php echo $data->agb_font_size != '' ? 'font-size: ' . $data->agb_font_size . 'px !important' : ''?>;
                <?php echo $data->agb_bg_color != '' ? 'background-color: ' . $data->agb_bg_color . ' !important' : ''?>;
            }
            .back_nxt_btns {
                <?php echo $data->next_back_width != '' ? 'width: ' . $data->next_back_width . 'px !important' : ''?>;
                <?php echo $data->next_back_height != '' ? 'height: ' . $data->next_back_height . 'px !important' : ''?>;
                <?php echo $data->next_back_font_size != '' ? 'font-size: ' . $data->next_back_font_size . 'px !important' : ''?>;
                <?php echo $data->next_back_loc_y != '' ? 'margin-top: ' . $data->next_back_loc_y . 'px !important' : ''?>;
                <?php echo $data->next_back_font != '' ? 'font-family: ' . $data->next_back_font : ''?>;
                <?php echo $data->next_back_font_color != '' ? 'color: ' . $data->next_back_font_color : ''?>;
            }
            .custom_btns {
                <?php echo $data->next_back_height != '' ? 'height: ' . $data->next_back_height . 'px !important' : ''?>;
                <?php echo $data->next_back_font_size != '' ? 'font-size: ' . $data->next_back_font_size . 'px !important' : ''?>;
                <?php echo $data->next_back_loc_y != '' ? 'margin-top: ' . $data->next_back_loc_y . 'px !important' : ''?>;
                <?php echo $data->next_back_font != '' ? 'font-family: ' . $data->next_back_font : ''?>;
                <?php echo $data->next_back_font_color != '' ? 'color: ' . $data->next_back_font_color : ''?>;
            }
            #back_button{
                display: ruby-text-container;
                <?php echo $data->next_back_loc_x != '' ? 'margin-left: ' . $data->next_back_loc_x . 'px !important' : ''?>;
            }
            #next_button{
                display: ruby-text-container;
                <?php echo $data->next_back_space_in_between != '' ? 'margin-left: ' . $data->next_back_space_in_between . 'px !important' : ''?>;
            }

            .f_service_name{
                <?php echo $data->sb_font_color != '' ? 'color: ' . $data->sb_font_color : ''?>;
            }
            .another_workflow_link{
                cursor:pointer;
                font-size:20px;
                margin-top:10px;
            }
			<?php if ($this->session->userdata('ticket_lang') == 'arabic') {?>
				@media (min-width: 992px) {
					.col-md-1,.col-md-10,.col-md-11,.col-md-12,.col-md-2,.col-md-3,.col-md-4,.col-md-5,.col-md-6,.col-md-7,.col-md-8,.col-md-9 {
						float: right;
					}
				}
			<?php }?>
        </style>
    </head>
    <body style="<?php echo $data->background_image ? 'background-image: url(' . base_url() . 'uploads/templates/' . $data->background_image . ')' : ''?>;<?php echo $data->background_color ? 'background-color: ' . $data->background_color : ''?>" >
        <div id='msg'>
            <ul id="noty_center_layout_container" class="error_msg_ul i-am-new" >
                <li class="made noty_container_type_success animated bounceIn" style="width: 310px; cursor: pointer; height: 86px;">
                    <div class="noty_bar noty_type_success" id="noty_1197577781054546000">
                        <div class="noty_message">
                            <span class="noty_text">
                                <div class="alert media fade in alert-danger">
                                    <p><strong>ERROR</strong> <span id="error_text"> </span> </p>
                                    <span class="error_dismiss_text"><?php echo $this->lang->line('click_to_dismiss')?></span>
                                </div>
                            </span>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div id='success'>
            <ul id="noty_center_layout_container" class="error_msg_ul i-am-new" >
                <li class="made noty_container_type_success animated bounceIn" style="width: 310px; cursor: pointer; height: 86px;">
                    <div class="noty_bar noty_type_success" id="noty_1197577781054546000">
                        <div class="noty_message">
                            <span class="noty_text">
                                <div class="alert media fade in alert-success">
                                    <p><strong>Success</strong> <?php echo $this->lang->line('op_success')?> </p><br/>
                                    <span class="error_dismiss_text"><?php echo $this->lang->line('click_to_dismiss')?></span>
                                </div>
                            </span>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="col-md-12">
            <div class="panel" style="opacity: 0.8;background-color: <?php echo $data->background_color ? $data->background_color : '#ffffff;'?>">
                <div>
                    <?php if ($this->session->userdata('ticket_lang') == 'arabic') {?>
                        <a  href="<?php echo base_url() ?>C_templates/set_customer_info/en/<?php echo $display_id?>" name="add_new" type="button" class="btn lb_buttons lb_buttons_offset_x" style=""><?php echo $this->lang->line('english')?></a>
                    <?php } else {?>
                        <a href="<?php echo base_url() ?>C_templates/set_customer_info/ar/<?php echo $display_id?>"  name="add_new" type="button" class="btn lb_buttons lb_buttons_offset_x" style=""><?php echo $this->lang->line('arabic')?></a>
                    <?php }?>
                </div>

                <?php if ($data->logo != '') {?>
                    <img src="<?php echo base_url()?>uploads/templates/<?php echo $data->logo?>" class="<?php echo $data->logo_alignment?>" width="<?php echo $data->logo_width?>px" height="<?php echo $data->logo_height?>px"/>
                    <div class="clearfix"></div>
                <?php }?>
                <div class="clearfix"></div>
                <div class="clearfix"></div>
                <div>
                    <?php if ($this->session->userdata('ticket_lang') == 'english') {?>
                        <h1 class="text-center header_text" ><?php echo ! empty($data->text_header_eng) ? $data->text_header_eng : ''?></h1>
                    <?php } else if ($this->session->userdata('ticket_lang') == 'arabic') {?>
                        <h1 class="text-center header_text" ><?php echo ! empty($data->text_header_arabic) ? $data->text_header_arabic : ''?></h1>
                    <?php }?>
                </div>
                <!-- Work flow categories -->
                <div class="tabNo_0 contetnt active" id="workCategories" index="0" >
                    <?php echo form_open('C_templates/generateTicket/null/' . $display_id . '/' . $selected_lang, 'id="servicesForm"'); ?>
                    <div class="steps">
                        <div id="step-0" class="step">
                            <?php
                                if (count($work_flow_cat) > 0) {
                                    foreach ($work_flow_cat as $cat) {
                                        if (! empty($cat)) {
                                            $action = 'show_child(' . $cat->id . ')';
                                            if (isset($cat->is_workflow) && $cat->cat_id === null) {
                                                $action = 'generate_ticket(' . $cat->id . ')';
                                            } else if (isset($cat->is_workflow) && $cat->cat_id === '0') {
                                                $action = 'show_service(' . $cat->id . ')';
                                            }
                                            if ($this->session->userdata('ticket_lang') == 'english') {
                                            ?>
                                            <div class="col-md-<?php echo count($work_flow_cat) > 1 ? '6' : '12'?> text-center">
                                                <button onclick="<?php echo $action?>" name="add_new" type="button" class="btn btn-primary btn-embossed sb_buttons" style="<?php echo $data->sb_image ? 'background-image: url(' . base_url() . 'uploads/templates/' . $data->sb_image . ');background-size: contain;background-position: center;' : ';'?>"><?php echo $cat->name?></button>
                                            </div>
                                            <?php
                                                } else if ($this->session->userdata('ticket_lang') == 'arabic') {
                                                            ?>
                                            <div class="col-md-<?php echo count($work_flow_cat) > 1 ? '6' : '12'?> text-center">
                                                <button onclick="<?php echo $action?>" name="add_new" type="button" class="btn btn-primary btn-embossed sb_buttons" style="<?php echo $data->sb_image ? 'background-image: url(' . base_url() . 'uploads/templates/' . $data->sb_image . ');background-size: contain;background-position: center;' : ';'?>"><?php echo $cat->ar_name?></button>
                                            </div>
                                            <?php
                                                }
                                                        }
                                                    }
                                                }
                                            ?>
                        </div>
                    </div>
                    <div class="clearfix"></div>
                    <?php if ($this->session->userdata('ticket_lang') == 'english') {?>
                        <button id="reprint_btn"  type="button" class="btn btn-primary btn-embossed custom_btns" style="<?php echo $data->next_back_n_img ? 'background-image: url(' . base_url() . 'uploads/templates/' . $data->next_back_n_img . ');background-size: contain;background-position: center;' : ';'?>" value="<?php echo $this->lang->line('reprint_ticket')?>"><?php echo $this->lang->line('reprint_ticket')?></button>
                        <div id="back_button" style="display:inline-block">
                            <button onClick="back_button_s()" name="back_button" type="button" class="btn btn-primary btn-embossed back_nxt_btns " style="<?php echo $data->next_back_b_img ? 'background-image: url(' . base_url() . 'uploads/templates/' . $data->next_back_b_img . ');background-size: contain;background-position: center;' : ';'?>"><?php echo $this->lang->line('back')?></button>
                        </div>
                        <div id="next_button" style="display:inline-block">
                            <button id="next_btn"  type="submit" class="btn btn-primary btn-embossed back_nxt_btns" style="<?php echo $data->next_back_n_img ? 'background-image: url(' . base_url() . 'uploads/templates/' . $data->next_back_n_img . ');background-size: contain;background-position: center;' : ';'?>" value="<?php echo $this->lang->line('next')?>"><?php echo $this->lang->line('next')?></button>
                        </div>
                    <?php } else {?>
                        <button id="reprint_btn"  type="button" class="btn btn-primary btn-embossed custom_btns" style="<?php echo $data->next_back_n_img ? 'background-image: url(' . base_url() . 'uploads/templates/' . $data->next_back_n_img . ');background-size: contain;background-position: center;' : ';'?>" value="<?php echo $this->lang->line('reprint_ticket_ar')?>"><?php echo $this->lang->line('reprint_ticket_ar')?></button>
                        <div id="back_button" style="display:inline-block">
                            <button onClick="back_button_s()" name="back_button" type="button" class="btn btn-primary btn-embossed back_nxt_btns" style="<?php echo $data->next_back_b_img ? 'background-image: url(' . base_url() . 'uploads/templates/' . $data->next_back_b_img . ');background-size: contain;background-position: center;' : ';'?>"><?php echo $this->lang->line('back_ar')?></button>
                        </div>
                        <div id="next_button" style="display:inline-block">
                            <button id="next_btn"  type="submit" onclick="this.disabled=true;" class="btn btn-primary btn-embossed back_nxt_btns" style="<?php echo $data->next_back_n_img ? 'background-image: url(' . base_url() . 'uploads/templates/' . $data->next_back_n_img . ');background-size: contain;background-position: center;' : ';'?>" value="<?php echo $this->lang->line('next')?>"><?php echo $this->lang->line('next_ar')?></button>
                        </div>
                    <?php }?>
                    <input type="hidden" name="cus_phone" value="<?php echo $this->session->userdata('cus_phone')?>" />
                    <input type="hidden" name="cus_reserv" value="<?php echo $this->session->userdata('cus_reserv')?>" />
                    <?php echo form_close(); ?>
                </div>
            </div>
        </div>
    </div>
    <script src="<?php echo base_url()?>assets/js/plugins.js"></script>
    <script src="<?php echo base_url()?>assets/plugins/icheck/icheck.min.js"></script>
    <script src="<?php echo base_url()?>assets/js/custom.js"></script>
    <script>
                                $("#msg").hide();
                                $("#success").hide();
<?php if ($this->session->flashdata('success')) {?>
                                    $("#success").show();
<?php }?>
<?php if ($this->session->flashdata('error')) {?>
                                    $("#error_text").html('<?php echo $this->session->flashdata('error')?>');
                                    $("#msg").show();
<?php }?>
                                $('body').click(function () {
                                    $("#success").hide();
                                    $("#msg").hide();
                                });
                                index = 0;
                                $(document).ready(function () {
                                    $('#next_button').hide();
                                    $('#back_button').hide();
                                });

                                function show_child(id) {
                                    $('#workCategories').show();
                                    $('#back_button').show();
                                    $(".step").hide();
                                    var csrf_test_name = get_cookie('csrf_cookie_name');
                                    $.ajax({
                                        url: "<?php echo base_url()?>C_templates/get_cat_ofBranch/" + id + '/' + <?php echo $display_id?>,
                                        datatype: 'JSON',
                                        data: {'<?php echo $this->security->get_csrf_token_name()?>': csrf_test_name},
                                        success: function (data1) {

                                            var data = JSON.parse(data1)

                                            if (data.length >= 1) {
                                                index++;
                                                var html = '<div id="step-' + index + '" class="step">';
<?php if ($this->session->userdata('ticket_lang') == 'english') {?>
                                                    for (var i = 0; i < data.length; i++) {
                                                        html += '<div class="col-md-12 text-center">\n\
                                                                                                                                                                                                                                                                                                                     \n\<button onclick="show_child(' + data[i]['id'] + ')"  name="' + data[i]['name'] + '" type="button" class="btn btn-primary btn-embossed sb_buttons" >' + data[i]['name'] + '</button>\n\
                                                                                                                                                                                                                                                                                                                         \n\</div>';
                                                    }
<?php } else {?>
                                                    //
                                                    for (var i = 0; i < data.length; i++) {
                                                        html += '<div class="col-md-12 text-center">\n\
                                                                                                                                                                                                                                                                                                                \n\<button onclick="show_child(' + data[i]['id'] + ')" name="' + data[i]['ar_name'] + '" type="button" class="btn btn-primary btn-embossed sb_buttons" >' + data[i]['ar_name'] + '</button>\n\
                                                                                                                                                                                                                                                                                                                    \n\</div>';
                                                    }

<?php }?>
                                                html += '</div>';
                                                $(".steps").append(html);
                                            } else {
                                                show_workFlow(id);
                                            }

                                        }
                                    });
                                }

                                function generate_ticket(workflow_id) {
                                    location.href = "<?php echo base_url()?>C_templates/generateTicket/" + workflow_id + '/' + <?php echo $display_id?> + '/' + '<?php echo $selected_lang?>';
                                }
                                function show_workFlow(id) {
                                    var csrf_test_name = get_cookie('csrf_cookie_name');
                                    $.ajax({
                                        url: "<?php echo base_url()?>C_templates/get_workFlow/" + id + '/' +<?php echo $display_id?>,
                                        datatype: "json",
                                        data: {'<?php echo $this->security->get_csrf_token_name()?>': csrf_test_name},
                                        success: function (data1) {
                                            var data = JSON.parse(data1);
                                            if (data.length >= 1) {
                                                index++;
                                                var html = '<div id="step-' + index + '" class="step">';
<?php if ($this->session->userdata('ticket_lang') == 'english') {?>
                                                    for (var x = 0; x < data.length; x++) {
                                                        html += '<div class="col-md-<?php echo $data->sb_per_row?> text-center">\n\
                                                                                                                                                                                                                                                                                                                     \n\<button onclick="show_service(' + data[x]['id'] + ')"  name="' + data[x]['name'] + '" type="button" class="btn btn-primary btn-embossed sb_buttons" >' + data[x]['name'] + '</button>\n\
                                                                                                                                                                                                                                                                                                                         \n\</div>';
                                                        //                                $('#workFlow').append(html);
                                                    }
<?php } else {?>
                                                    for (var i = 0; i < data.length; i++) {
                                                        html += '<div class="col-md-<?php echo $data->sb_per_row?> text-center">\n\
                                                                                                                                                                                                                                                                                                                \n\<button onclick="show_service(' + data[i]['id'] + ')" name="' + data[i]['ar_name'] + '" type="button" class="btn btn-primary btn-embossed sb_buttons" >' + data[i]['ar_name'] + '</button>\n\
                                                                                                                                                                                                                                                                                                                    \n\</div>';
                                                        //                                $('#workFlow').append(html);
                                                    }
<?php }?>
                                                html += '</div>';
                                                $(".steps").append(html);
                                            }
                                        }
                                    });
                                }

                                function show_service(id) {
                                    services_count = 0;
									var csrf_test_name = get_cookie('csrf_cookie_name');
                                    $.ajax({
                                        url: "<?php echo base_url()?>C_templates/get_workflow_steps/" + id,
                                        datatype: "json",
                                        data: {'<?php echo $this->security->get_csrf_token_name()?>': csrf_test_name},
                                        success: function (data1) {
                                            var data = JSON.parse(data1);
                                            if (data.constructor !== Array) {
                                                alert(data);
                                                return;
                                            }
                                            $(".step").hide();
                                            if (data.length >= 1) {
                                                $('#next_button').show();
                                            }
                                            $('#back_button').show();
                                            if (data.length >= 1) {
        $(".another_workflow_link").hide();
                                                index++;

                                                var html3 = '<div id="step-' + index + '" class="step_final">';
                                                html3 += '<input type="hidden" name="workflow_id' + services_count + '" value="' + id + '">';

<?php if ($this->session->userdata('ticket_lang') == 'english') {?>

                                                    for (var i = 0; i < data.length; i++) {
                                                        //onchange="check_agent(' + data[i]['can_choose_agent'] + ',' + data[i]['service_id'] + ', this)"
                                                        html3 += '<div class="col-md-12 text-center">\n\
                                                                                                                                                                                                                                                                                                                        <input type="checkbox" class="service_checks" data-checkbox="icheckbox_square-blue" data-can_choose_agent="' + data[i]['can_choose_agent'] + '" data-service_id="' + data[i]['service_id'] + '"  name="Agent_services' + (i + services_count) + '"value="' + data[i]['service_name'] + '"' + (data.length == 1 ? 'checked disabled' : '') +'>\n\
                                                                                                                                                                                                                                                                                                                        <label class="f_service_name">' + data[i]['service_name'] + '</label>\n\
                                                                                                                                                                                                                                                                                                                        <input type="hidden" name="seviceId' + (i + services_count) + '" value="' + data[i]['service_id'] + '">\n\
                                                                                                                                                                                                                                                                                                                        <div class="choose_agent"></div></div>';

                                                        //                                $('#servicesOptions').append(html3);
                                                    }
<?php } else {?>
                                                    for (var i = 0; i < data.length; i++) {
                                                        //onchange="check_agent(' + data[i]['can_choose_agent'] + ',' + data[i]['service_id'] + ', this)"
                                                        html3 += '<div class="col-md-12 text-center">\n\
                                                                                                                                                                                                                                                                                                                    <input type="checkbox" class="service_checks" data-checkbox="icheckbox_square-blue" data-can_choose_agent="' + data[i]['can_choose_agent'] + '" data-service_id="' + data[i]['service_id'] + '"  name="Agent_services' + (i + services_count) + '"value="' + data[i]['arabic_service_name'] + '"' + (data.length == 1 ? 'checked disabled' : '') +'>\n\
                                                                                                                                                                                                                                                                                                                    <label class="f_service_name">' + data[i]['arabic_service_name'] + '</label>\n\
                                                                                                                                                                                                                                                                                                                    <input type="hidden" name="seviceId' + (i + services_count) + '" value="' + data[i]['service_id'] + '">\n\
                                                                                                                                                                                                                                                                                                                    <div class="choose_agent"></div></div>';
                                                        //                                $('#servicesOptions').append(html3);

                                                    }
<?php }?>
                                                html3 += '</div>';
                                                //html3 += '<div class="col-md-12 text-center"><a class="another_workflow_link" onclick="add_another_workflow()"><?php echo $this->session->userdata('ticket_lang') == 'english' ? 'Add another workfow' : 'أضف سير عمل آخر'?></a>';
                                                if ($(".step_final").length > 0) {
                                                    html3 += '<br/><a class="another_workflow_link delete_workflow_link" onclick="delete_last_workflow()"><?php echo $this->session->userdata('ticket_lang') == 'english' ? 'Delete workfow' : 'مسح سير العمل'?></a>';
                                                }
                                                html3 += '</div>';
                                                services_count += data.length;
                                                $(".steps").append(html3);
                                                handleiCheck();
                                                $(document).on('ifChanged', '.service_checks', function () {
                                                    check_agent($(this).data('can_choose_agent'), $(this).data('service_id'), this);
                                                });
                                                $('input').each(function () {
                                                    //$(this).iCheck('check');
                                                    //$(this).iCheck('disable');
                                                });
                                                let auto_next = <?php echo AUTO_PRESS_NEXT_DELAY_MS?>;
												if(data.length == 1 && auto_next > 0) {
                                                    $("#next_btn").attr('disabled', true);
												setTimeout(() => {

													let chkAgent = data[0]['can_choose_agent'];
													if(chkAgent == true) {
														let elm = document.getElementsByName("Agent_services0");
														check_agent(data[0]['can_choose_agent'], data[0]['service_id'], elm);
													} else {
														Form_Submit();
													}
}, auto_next);
												}
                                            }
                                        }
                                    });
                                }
                                function check_agent(Agent_avail, service_id, element) {
                                    var checkbox_name = $(element).prop('name');
                                    var index = checkbox_name.slice(checkbox_name.indexOf('Agent_services') + 14);
                                    if (!$(element).prop('checked')) {
                                        $(element).parent('div').parent('div').find('.choose_agent').html('');
                                        return;
                                    }
                                    if (Agent_avail == 1) {
                                        var csrf_test_name = get_cookie('csrf_cookie_name');
                                        $.ajax({
                                            url: "<?php echo base_url()?>C_templates/get_service_agent/" + service_id,
                                            dataType: "json",
                                            data: {'<?php echo $this->security->get_csrf_token_name()?>': csrf_test_name},
                                            success: function (data) {
                                                if (Agent_avail == 1) {
                                                    var html = '<select name="service_agent' + index + '" id="window_no" class="form-control all_terminals modal-content" data-search="true" >';
                                                    var value, extra_text;
                                                    for (var i = 0; i < data.length; i++) {
                                                        extra_text = '';
                                                        if (data[i]['terminalID'] == null) {
                                                            value = 'A_' + data[i]['id'];
                                                            //extra_text = '-- not active now';
                                                            html += '<option value="' + value + '">' + data[i]['name'] + extra_text + ' </option>';
                                                        } else {
                                                            value = 'A_' + data[i]['id'];
                                                            html += '<option value="' + value + '">' + data[i]['name'] + extra_text + ' </option>';
                                                        }
                                                    }
                                                    html += '</select>';
                                                    $(element).parent('div').parent('div').find('.choose_agent').html(html);
                                                }
                                            }
                                        });
                                    }
                                }

                                $("#next_btn").on("click", function (e) {
                                    e.preventDefault();
                                    Form_Submit();
                                });

                                function Form_Submit() {
                                    $('.service_checks').each(function () {
                                        $(this).removeAttr('disabled');
                                    });
                                    var csrf_test_name = get_cookie('csrf_cookie_name');
                                    $("input[name=csrf_test_name]").val(csrf_test_name);
                                    $('#servicesForm').submit();
                                }

                                function back_button_s() {
                                    index--;
                                    if (index < 0) {
                                        index = 0;
                                    }
                                    $(".step").hide();
                                    $("#step-" + index).show();
                                    /*if (index == 0) {
                                        $('#back_button').hide();
                                    }*/
                                    if ($(".step_final").length >= 1) {
                                        $(".step_final").remove();
                                    }
                                    $(".another_workflow_link").hide();
                                    $('#next_button').hide();

                                }

                                function add_another_workflow() {
                                    $("#step-0").insertAfter(".steps");
                                    $('#step-0').show();
                                }

                                function delete_last_workflow() {
                                    $(".step_final").last().remove();
                                    if ($(".step_final").length == 1) {
                                        $('.delete_workflow_link').hide();
                                    }
                                }

                                $("#reprint_btn").on("click", function (e) {
                                    window.location.href = '<?php echo base_url()?>C_templates/reprintTicket/<?php echo substr(! empty($this->session->userdata('ticket_lang')) ? $this->session->userdata('ticket_lang') : 'arabic', 0, 2)?>/<?php echo $display_id?>';
                                });
    </script>
</body>
</html>