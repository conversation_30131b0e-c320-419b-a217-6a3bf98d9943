<?php

defined('BASEPATH') or exit('No direct script access allowed');
require_once APPPATH . "/third_party/spout/src/Spout/Autoloader/autoload.php";

use Box\Spout\Reader\Common\Creator\ReaderEntityFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class C_templates extends CI_check_login
{
    public function __construct()
    {
        $this->require_login = false;
        parent::__construct();
        $this->load->model('m_templates');
        $this->load->model('M_general');
        $this->load->model('M_terminal');
        $this->load->model('Crud_model');
        $this->lang->load('eng');
        $this->lang->load('eng_2');
        global $Child_counter;
        $Child_counter = 0;
        $this->load->library('ci_qr_code');
    }

    public function add_form()
    {
        if ($this->session->userdata('login')) {
            $arr['active'] = 'temp';
            CI_load_view::load_view('template/template_form', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function load_edit_form($id)
    {
        if ($this->session->userdata('login')) {
            $arr['data'] = $this->m_templates->get_data_with_id($id);
            if (!$arr['data']) {
                $error['text'] = 'Sorry, Tempplate not Found';
                $this->load->view('private/page-500', $error);
            }
            $arr['active'] = 'temp';
            CI_load_view::load_view('template/template_form', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function check()
    {
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('name', 'Template Name', 'required');
            if ($this->form_validation->run() == false) {
                echo validation_errors();
            } else {
                echo 'yes';
            }
        }
    }

    public function save_data()
    {
        if ($this->session->userdata('login')) {
            $temp_name = $this->input->post('name');
            $temp_desc = $this->input->post('desc');
            $this->m_templates->insert_data($temp_name, $temp_desc);
            record_log('Adding Template : ' . $temp_name);
            redirect(base_url() . 'C_templates/loadShow/success');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function edit_data($id)
    {
        if ($this->session->userdata('login')) {
            $temp_name = $this->input->post('name');
            $temp_desc = $this->input->post('desc');
            $this->m_templates->edit_data($temp_name, $temp_desc, $id);
            record_log('Update Template : ' . $temp_name);
            redirect(base_url() . 'C_templates/loadShow/success');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function loadShow($extra_data = '')
    {
        if ($this->session->userdata('login')) {
            $arr['extra_data'] = $extra_data;
            $arr['data'] = $this->m_templates->get_all_data();
            $arr['active'] = 'temp';
            CI_load_view::load_view('template/template_show', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete($id)
    {
        if ($this->session->userdata('login')) {
            $this->m_templates->delete_record($id);
            record_log('Delete Template :template id ' . $id);
            redirect(site_url() . 'C_templates/loadShow/success');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function template($id)
    {
        if ($this->session->userdata('login')) {
            $arr['temp_data'] = $this->m_templates->get_template_data($id);
            if (!$arr['temp_data']) {
                $error['text'] = $this->lang->line('temp_not_found');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr['active'] = "temp";
            CI_load_view::load_view('template/template', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function template_general_settings($id, $success = null)
    {
        if ($this->session->userdata('login')) {
            if (!$this->m_templates->branch_deleted_or_not_exist($id)) {
                $arr['temp_data'] = $this->m_templates->get_branch_data($id);
                $arr['services'] = $this->m_templates->get_services($id);
                $arr['template_services'] = $this->m_templates->get_template_services($id);
                $arr['not_vr_pro_services'] = $this->m_templates->get_not_assigned_pro_services($id); //get pro services of this temp but not added as VR services
                $arr['temp_vr_services'] = $this->m_templates->get_template_vr_services($id);
                $arr['service_escalation'] = $this->m_templates->get_service_escalations($id);
                $arr['template_settings'] = $this->m_templates->get_template_settings($id);
                $arr['template_terminals'] = $this->m_templates->get_temp_terminals($id);
                $arr['priorities'] = $this->m_templates->get_priorities();
                $arr['active'] = 'temp';
                if ($success != null) {
                    $arr['success'] = 'success';
                }
                CI_load_view::load_view('template/template_general_sett', $arr);
            } else {
                $error['text'] = $this->lang->line('temp_not_found');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('temp_not_found');
            $this->load->view('private/page-500', $error);
        }
    }

    public function advanced_settings($branch_id, $success = null)
    {
        if ($this->session->userdata('login')) {
            if (get_p("advanced_settings_lvl", "v")) {
                $this->load->model('M_general');
                $user_brnches = $this->M_general->get_user_branches();
                if (!in_array($branch_id, $user_brnches)) {
                    $error['text'] = $this->lang->line('permission_to_branch');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['template_settings'] = $this->m_templates->get_advanced_sett($branch_id);
                $arr['active'] = 'advanced';
                if ($success != null) {
                    $arr['success'] = 'success';
                }
                $arr['branch_id'] = $branch_id;
                $arr['branch_name'] = $this->m_templates->get_branch_name($branch_id)->EnglishBranchName;
                CI_load_view::load_view('branch/advanced_settings', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function general_ticket_settings($branch_id, $success = null)
    {
        if ($this->session->userdata('login')) {
            if (get_p("tic_template_sett_lvl", "v")) {
                $this->load->model('M_general');
                $user_brnches = $this->M_general->get_user_branches();
                if (!in_array($branch_id, $user_brnches)) {
                    $error['text'] = $this->lang->line('permission_to_branch');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['template_settings'] = $this->m_templates->get_general_ticket_sett($branch_id);
                $arr['active'] = 'general_ticket';
                if ($success != null) {
                    $arr['success'] = 'success';
                }
                $arr['branch_id'] = $branch_id;
                $arr['branch_name'] = $this->m_templates->get_branch_name($branch_id)->EnglishBranchName;
                CI_load_view::load_view('branch/general_ticket_sett', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function vr_services($branch_id, $success = null, $error = null, $temp_service_id = null)
    {
        if ($this->session->userdata('login')) {
            $this->load->model('M_general');
            $user_brnches = $this->M_general->get_user_branches();
            if (!in_array($branch_id, $user_brnches)) {
                $error['text'] = $this->lang->line('permission_to_branch');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr['temp_vr_services'] = $this->m_templates->get_template_vr_services($branch_id);
            $arr['not_vr_pro_services'] = $this->m_templates->get_pro_services();
            $arr['active'] = 'manage_service';
            $arr['branch_id'] = $branch_id;
            $arr['branch_name'] = $this->m_templates->get_branch_name($branch_id)->EnglishBranchName;
            if ($success != null) {
                $arr['success'] = $success;
            }
            if ($success == 'edit') {
                $arr['temp_service_id'] = $temp_service_id;
            }
            if ($error != null) {
                $arr['error'] = $error;
            }
            CI_load_view::load_view('branch/vr_service', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function add_service($temp_id)
    {
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('ser_eng_name', 'Service english name', 'required');
            $this->form_validation->set_rules('ser_ar_name', 'Service arabic name', 'required');
            $this->form_validation->set_rules('service_num', 'Service num.', 'required|integer|callback_unique_service_num');
            $this->form_validation->set_rules('service', 'Wam Pro Service', 'required');
            $this->form_validation->set_rules('sms_en', 'SMS english text', 'max_length[160]');
            $this->form_validation->set_rules('sms_ar', 'SMS arabic text', 'max_length[70]');
            if ($this->form_validation->run() == false) {
                $this->vr_services($temp_id, null, validation_errors());
            } else {
                $temp_pro_service_id = $this->input->post('service');
                $eng_name = $this->input->post('ser_eng_name');
                $ar_name = $this->input->post('ser_ar_name');

                if ($this->input->post('active') != null) {
                    $active = 1;
                } else {
                    $active = 0;
                }
                if ($this->input->post('has_input') != null) {
                    $has_input = 1;
                } else {
                    $has_input = 0;
                }

                if ($this->input->post('officer') != null) {
                    $officer = 1;
                } else {
                    $officer = 0;
                }
                $sms_ar = $this->input->post('sms_ar');
                $sms_en = $this->input->post('sms_en');
                $service_num = $this->input->post('service_num');
                $instructions = $this->input->post('instructions');
                $show_res = $this->input->post('show_res');
                $this->m_templates->add_service_to_template($temp_pro_service_id, $eng_name, $ar_name, $active, $has_input, $officer, $temp_id, $sms_ar, $sms_en, $service_num, $instructions, $show_res);
                update_branch_sett('vr_services', $temp_id);
                record_log('Update service number : ' . $service_num);
                redirect(base_url() . 'C_templates/vr_services/' . $temp_id . '/success');
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function unique_service_num()
    {
        if ($this->session->userdata('login')) {
            $branch_id = $this->input->post('branch_id');
            $existance_count = $this->db->get_where('template_vr_services', array(
                'service_num' => $this->input->post('service_num'), 'branch_id' => $branch_id))->num_rows();
            if ($existance_count > 0) {
                $this->form_validation->set_message('unique_service_num', 'The {field} field must be unique');
                return false;
            }
            return true;
        }
    }

    public function get_template_data($temp_id)
    {
        if ($this->session->userdata('login')) {
            echo json_encode($this->m_templates->get_temp_service_assignment_data($temp_id));
        }
    }

    public function get_template_vr_service_data($temp_vr_service_id)
    {
        if ($this->session->userdata('login')) {
            echo json_encode($this->m_templates->get_temp_vr_service_data($temp_vr_service_id));
        }
    }

    public function save_edit_service_assignment($temp_service_id)
    {
        if ($this->session->userdata('login')) {
            $temp_id = $this->m_templates->get_template_id_from_vr_service($temp_service_id);
            $this->form_validation->set_rules('eng_name_edit', 'Service english name', 'required');
            $this->form_validation->set_rules('ar_name_edit', 'Service arabic name', 'required');
            $this->form_validation->set_rules('service_edit', 'Wam Pro Service', 'required');
            $this->form_validation->set_rules('service_num_edit', 'Service num.', 'required|integer|callback_unique_service_num_edit[' . $temp_service_id . ']');
            $this->form_validation->set_rules('sms_en_edit', 'SMS english text', 'max_length[160]');
            $this->form_validation->set_rules('sms_ar_edit', 'SMS arabic text', 'max_length[70]');
            if ($this->form_validation->run() == false) {
                $this->vr_services($temp_id, 'edit', validation_errors(), $temp_service_id);
                return;
            } else {
                $temp_pro_service_id = $this->input->post('service_edit');
                $eng_name = $this->input->post('eng_name_edit');
                $ar_name = $this->input->post('ar_name_edit');
                $sms_ar = $this->input->post('sms_ar_edit');
                $sms_en = $this->input->post('sms_en_edit');
                $service_num = $this->input->post('service_num_edit');
                $instructions = $this->input->post('instructions');
                $show_res = $this->input->post('show_res');
                if ($this->input->post('active_edit') != null) {
                    $active = 1;
                } else {
                    $active = 0;
                }
                if ($this->input->post('has_input_edit') != null) {
                    $has_input = 1;
                } else {
                    $has_input = 0;
                }
                if ($this->input->post('officer_edit') != null) {
                    $officer = 1;
                } else {
                    $officer = 0;
                }
                $this->m_templates->update_template_service($temp_pro_service_id, $eng_name, $ar_name, $active, $has_input, $officer, $temp_service_id, $sms_en, $sms_ar, $temp_id, $service_num, $instructions, $show_res);
                update_branch_sett('vr_services', $temp_id);
                record_log('Update service number : ' . $service_num);
                redirect(base_url() . 'C_templates/vr_services/' . $temp_id . '/success');
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function unique_service_num_edit($service_num, $vr_service_id)
    {
        if ($this->session->userdata('login')) {
            $branch_id = $this->m_templates->get_branch_from_vr_service($vr_service_id);
            $existance_count = $this->db->get_where('template_vr_services', array(
                'service_num' => $service_num, 'branch_id' => $branch_id, 'id !=' => $vr_service_id))->num_rows();
            if ($existance_count > 0) {
                $this->form_validation->set_message('unique_service_num_edit', 'The {field} field must be unique');
                return false;
            }
            return true;
        }
    }

    public function save_edit_pro_service_assignment($temp_service_id)
    {
        if ($this->session->userdata('login')) {
            $service = $this->input->post('service_pro_edit');
            $start_value = $this->input->post('start_value_edit');
            $temp_id = $this->m_templates->get_template_id($temp_service_id);
            $this->m_templates->update_template_pro_service($service, $start_value, $temp_service_id, $temp_id);
            record_log('Update pro-service number : ' . $service);
            redirect(base_url() . 'C_templates/template_general_settings/' . $temp_id . '/success');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete_temp_service($temp_service_id)
    {
        if ($this->session->userdata('login')) {
            $temp_id = $this->m_templates->get_template_id($temp_service_id);
            $this->db->delete('template_pro_services', array('id' => $temp_service_id));
            $this->m_templates->insert_operation_on_temp('update', $temp_id);
            record_log('Delete template pro-service  : id ' . $temp_service_id);
            redirect(base_url() . 'C_templates/template_general_settings/' . $temp_id . '/success');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete_vr_service($temp_vr_service_id)
    {
        if ($this->session->userdata('login')) {
            $temp_id = $this->m_templates->get_template_id_from_vr_service($temp_vr_service_id);
            $this->db->delete('template_vr_services', array('id' => $temp_vr_service_id));
            $this->m_templates->insert_operation_on_temp('update', $temp_id);
            update_branch_sett('vr_services', $temp_id);
            record_log('Delete vr_service : id ' . $temp_vr_service_id);
            redirect(base_url() . 'C_templates/vr_services/' . $temp_id . '/success');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function check_service_escalation($temp_id)
    {
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('from_service', 'From Service', 'required|integer');
            $this->form_validation->set_rules('to_service', 'To Service', 'required|integer');
            $this->form_validation->set_rules('esc_time', 'Waiting Time', 'required|numeric');
            if ($this->form_validation->run() == false) {
                echo validation_errors();
            } else {
                if ($this->m_templates->check_redundant_escalation($temp_id, $this->input->post('from_service'), $this->input->post('to_service'))) {
                    echo $this->lang->line('redundant_escalation');
                } elseif ($this->input->post('from_service') == $this->input->post('to_service')) {
                    echo $this->lang->line('same_service');
                } else {
                    echo 'yes';
                }
            }
        }
    }

    public function check_edit_service_escalation($temp_id)
    {
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('from_service_edit', $this->lang->line('from_service'), 'required|integer');
            $this->form_validation->set_rules('to_service_edit', $this->lang->line('to_service'), 'required|integer');
            $this->form_validation->set_rules('esc_time_edit', $this->lang->line('waiting_time'), 'required|numeric');
            if ($this->form_validation->run() == false) {
                echo validation_errors();
            } else {
                $service_escalation_id = $this->input->post('service_escalation_id');
                if ($this->m_templates->check_redundant_escalation_edit($temp_id, $this->input->post('from_service_edit'), $this->input->post('to_service_edit'), $service_escalation_id)) {
                    echo $this->lang->line('redundant_escalation');
                } elseif ($this->input->post('from_service_edit') == $this->input->post('to_service_edit')) {
                    echo $this->lang->line('same_service');
                } else {
                    echo 'yes';
                }
            }
        }
    }

    public function add_service_escalation($temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("service_escalation_lvl", "c")) {
                $from_service = $this->input->post('from_service');
                $to_service = $this->input->post('to_service');
                $waiting_time = $this->input->post('esc_time');
                $this->m_templates->add_service_escalation($from_service, $to_service, $waiting_time, $temp_id);
                update_branch_sett('service_escalation', $temp_id);
                record_log('Adding service escalation');
                redirect(base_url() . 'C_service/service_escalation/' . $temp_id . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_service_escalation_data($service_esc_id)
    {
        if ($this->session->userdata('login')) {
            echo json_encode($this->m_templates->get_service_escalation_data($service_esc_id));
        }
    }

    public function save_edit_service_escalation($service_esc_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("service_escalation_lvl", "u")) {
                $from_service = $this->input->post('from_service_edit');
                $to_service = $this->input->post('to_service_edit');
                $waiting_time = $this->input->post('esc_time_edit');
                $temp_id = $this->m_templates->get_template_id_from_escalation($service_esc_id);
                $this->m_templates->save_edit_service_escalation($from_service, $to_service, $waiting_time, $service_esc_id, $temp_id);
                update_branch_sett('service_escalation', $temp_id);
                record_log('Update service escalation : id ' . $service_esc_id);
                redirect(base_url() . 'C_service/service_escalation/' . $temp_id . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete_service_escalation($service_escalation_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("service_escalation_lvl", "d")) {
                $temp_id = $this->m_templates->get_template_id_from_escalation($service_escalation_id);
                $this->db->delete('template_service_escalation', array('id' => $service_escalation_id));
                update_branch_sett('service_escalation', $temp_id);
                record_log('Delete service escalation : id ' . $service_escalation_id);
                redirect(base_url() . 'C_service/service_escalation/' . $temp_id . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function save_ticket_settings($temp_id)
    {
        if ($this->session->userdata('login')) {
            $general_lang = $this->input->post('general_lang');
            $service_lang = $this->input->post('service_lang');
            $adv_font = $this->input->post('adv_font');
            $name_font = $this->input->post('name_font');
            $font_size = $this->input->post('font_size');
            $num_lang = $this->input->post('num_lang');
            $print_ter_num = 0;
            $expec_wait_time = 0;
            $num_wait_client = 0;
            $print_adv = 0;
            $print_customer_name = 0;
            $general_graphic = 0;
            $service_graphic = 0;
            if ($this->input->post('print_ter_num') != null) {
                $print_ter_num = 1;
            }
            if ($this->input->post('expec_wait_time') != null) {
                $expec_wait_time = 1;
            }
            if ($this->input->post('num_wait_client') != null) {
                $num_wait_client = 1;
            }
            if ($this->input->post('print_adv') != null) {
                $print_adv = 1;
            }
            if ($this->input->post('print_customer_name') != null) {
                $print_customer_name = 1;
            }
            if ($this->input->post('general_graphic') != null) {
                $general_graphic = 1;
            }
            if ($this->input->post('service_graphic') != null) {
                $service_graphic = 1;
            }
            $data = array(
                'general_lang' => $general_lang,
                'service_lang' => $service_lang,
                'font_size' => $font_size,
                'adv_font' => $adv_font,
                'name_font' => $name_font,
                'print_terminal_num' => $print_ter_num,
                'expec_waiting_time' => $expec_wait_time,
                'waiting_customers_num' => $num_wait_client,
                'print_adv' => $print_adv,
                'print_customer_name' => $print_customer_name,
                'general_graphics' => $general_graphic,
                'service_graphics' => $service_graphic,
                'numeral' => $num_lang,
                'template_id' => $temp_id,
            );
            $this->m_templates->save_temp_settings('hr_ticket_sett', $data);
            update_branch_sett('general_ticket_sett', $temp_id);
            record_log('Adding ticket_settings');
            redirect(base_url() . 'C_templates/general_ticket_settings/' . $temp_id . '/success');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function check_add_pro_service()
    {
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('service', 'Service', 'required|integer');
            $this->form_validation->set_rules('start_value', $this->lang->line('start_value'), 'required|integer');
            if ($this->form_validation->run() == false) {
                echo validation_errors();
            } else {
                echo 'yes';
            }
        }
    }

    public function check_edit_pro_service()
    {
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('service_pro_edit', 'Service', 'required|integer');
            $this->form_validation->set_rules('start_value_edit', $this->lang->line('start_value'), 'required|integer');
            if ($this->form_validation->run() == false) {
                echo validation_errors();
            } else {
                echo 'yes';
            }
        }
    }

    public function add_pro_service($temp_id)
    {
        if ($this->session->userdata('login')) {
            $service = $this->input->post('service');
            $start_value = $this->input->post('start_value');
            $this->m_templates->save_temp_pro_service($service, $start_value, $temp_id);
            record_log('Adding pro_service');
            redirect(base_url() . 'C_templates/template_general_settings/' . $temp_id);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function arrange_services($temp_id)
    {
        if ($this->session->userdata('login')) {
            $this->m_templates->arrange_services($this->input->post('service_arrange'), $temp_id);
            update_branch_sett('vr_services', $temp_id);
            redirect(base_url() . 'C_templates/vr_services/' . $temp_id . '/success');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function save_standalone_ticket_settings($temp_id, $is_english = 0)
    {
        if ($this->session->userdata('login')) {
            if (get_p("tic_template_sett_lvl", "u")) {
                if (!$is_english) {
                    $this->form_validation->set_rules('comp_name_font_size', 'company name font size', 'trim|numeric');
                    $this->form_validation->set_rules('date_font_size', 'Date font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('expected_time_font_size', 'Expected time font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('service_name_font_size', 'Service name font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('branch_name_font_size', 'Branch name font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('time_font_size', 'Time font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('ticket_num_font_size', 'Ticket number font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('waiting_customers_font_size', 'Waiting customers font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('footer_ad_font_size', 'Footer (Ad) font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('header_ad_font_size', 'Header (Ad) font size', 'trim|required|numeric');
                } else {
                    $this->form_validation->set_rules('comp_name_font_size_e', 'company name font size', 'trim|numeric');
                    $this->form_validation->set_rules('date_font_size_e', 'Date font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('expected_time_font_size_e', 'Expected time font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('service_name_font_size_e', 'Service name font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('branch_name_font_size_e', 'Branch name font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('time_font_size_e', 'Time font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('ticket_num_font_size_e', 'Ticket number font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('waiting_customers_font_size_e', 'Waiting customers font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('footer_ad_font_size_e', 'Footer (Ad) font size', 'trim|required|numeric');
                    $this->form_validation->set_rules('header_ad_font_size_e', 'Header (Ad) font size', 'trim|required|numeric');
                }
                if ($this->form_validation->run() == false) {
                    $this->template_ticket_settings($temp_id, null, validation_errors());
                } else {
                    if (!$is_english) {
                        //update for english form
                        $data = array(
                            'template_id' => $temp_id,
                            'ticket_lang' => 'arabic',
                            'comp_name_font' => $this->input->post('com_font'),
                            'comp_name_aligment' => $this->input->post('com_ali'),
                            'date_font' => $this->input->post('date_font'),
                            'date_aligment' => $this->input->post('date_ali'),
                            'expected_time_font' => $this->input->post('expec_font'),
                            'expected_time_aligment' => $this->input->post('expec_ali'),
                            'terminal_id_font' => $this->input->post('ter_font'),
                            'terminal_id_aligment' => $this->input->post('ter_ali'),
                            'service_name_font' => $this->input->post('ser_font'),
                            'service_name_aligment' => $this->input->post('ser_ali'),
                            'branch_name_font' => $this->input->post('branch_font'),
                            'branch_name_aligment' => $this->input->post('branch_ali'),
                            'time_font' => $this->input->post('time_font'),
                            'time_aligment' => $this->input->post('time_ali'),
                            'ticket_num_font' => $this->input->post('ticket_font'),
                            'ticket_num_aligment' => $this->input->post('ticket_ali'),
                            'waiting_customers_font' => $this->input->post('cus_num_font'),
                            'waiting_customers_aligment' => $this->input->post('cus_num_ali'),
                            'ticket_img_width' => $this->input->post('tic_img_width'),
                            'ticket_img_height' => $this->input->post('tic_img_height'),
                            'tabs_before_img' => $this->input->post('tic_img_tabs'),
                            'comp_name_font_size' => $this->input->post('comp_name_font_size'),
                            'date_font_size' => $this->input->post('date_font_size'),
                            'expected_time_font_size' => $this->input->post('expected_time_font_size'),
                            'terminal_id_font_size' => !empty($this->input->post('terminal_id_font_size')) ? $this->input->post('terminal_id_font_size') : 0,
                            'service_name_font_size' => $this->input->post('service_name_font_size'),
                            'branch_name_font_size' => $this->input->post('branch_name_font_size'),
                            'time_font_size' => $this->input->post('time_font_size'),
                            'ticket_num_font_size' => $this->input->post('ticket_num_font_size'),
                            'waiting_customers_font_size' => $this->input->post('waiting_customers_font_size'),
                            'footer_ad_font_size' => $this->input->post('footer_ad_font_size'),
                            'footer_ad_font' => $this->input->post('footer_ad_font'),
                            'footer_ad_aligment' => $this->input->post('footer_ad_ali'),
                            'header_ad_font_size' => $this->input->post('header_ad_font_size'),
                            'header_ad_font' => $this->input->post('header_ad_font'),
                            'header_ad_aligment' => $this->input->post('header_ad_ali'),
                            'logo_ali' => $this->input->post('logo_ali'),
                            'show_comp_n_block' => $this->input->post('show_comp_n_block') ? 1 : 0,
                            'show_date_block' => $this->input->post('show_date_block') ? 1 : 0,
                            'show_exp_time_block' => $this->input->post('show_exp_time_block') ? 1 : 0,
                            'show_service_n_block' => $this->input->post('show_service_n_block') ? 1 : 0,
                            'show_ticket_img_block' => $this->input->post('show_ticket_img_block') ? 1 : 0,
                            'show_branch_n_block' => $this->input->post('show_branch_n_block') ? 1 : 0,
                            'show_time_block' => $this->input->post('show_time_block') ? 1 : 0,
                            'show_ticket_num_block' => $this->input->post('show_ticket_num_block') ? 1 : 0,
                            'show_wait_cust_num_block' => $this->input->post('show_wait_cust_num_block') ? 1 : 0,
                            'show_footer_block' => $this->input->post('show_footer_block') ? 1 : 0,
                            'show_header_block' => $this->input->post('show_header_block') ? 1 : 0,
                        );
                        if ($_FILES['header_img']['name'] != '') {
                            $config['upload_path'] = 'uploads/';
                            $config['allowed_types'] = 'gif|jpg|png|jpeg|bmp';
                            $this->load->library('upload', $config);
                            if (!$this->upload->do_upload('header_img') && $_FILES['header_img']['error'] != 4) { //Error Occured
                                echo '<br/> Ticket Image: ' . $this->upload->display_errors() . '<br/><a href="' . base_url() . 'C_templates/template_general_settings/' . $temp_id . '/#tab1_5" >Back</a>';
                                die();
                            } else {
                                $data['ticket_image'] = $this->upload->data()['file_name'];
                            }
                        }
                        $this->db->select('id');
                        $row = $this->db->get_where('vr_ticket_sett', array('template_id' => $data['template_id'], 'ticket_lang' => 'arabic'))->row();
                        if (!check_is_server()) {
                            $data['edited_on_client'] = 1;
                        }
                        if ($row) {
                            $this->db->where('id', $row->id);
                            $this->db->update('vr_ticket_sett', $data);
                            $insetrted_id = $row->id;
                        } else {
                            $this->db->insert('vr_ticket_sett', $data);
                            $insetrted_id = $this->db->insert_id();
                        }
                        if ($this->db->dbdriver == 'sqlsrv') {
                            $this->db->query('UPDATE "vr_ticket_sett" SET "ar_footer_text" = N\'' . $this->input->post('ar_foot_text') . '\' , "sms_ar_text" = N\'' . $this->input->post('sms_ar_text') . '\' WHERE "id" = ' . $insetrted_id);
                        } else {
                            $this->db->query('UPDATE vr_ticket_sett SET ar_footer_text = \'' . $this->input->post('ar_foot_text') . '\' , sms_ar_text = \'' . $this->input->post('sms_ar_text') . '\' WHERE id = ' . $insetrted_id);
                        }
                    } else {
                        //update for english form
                        $data_e = array(
                            'template_id' => $temp_id,
                            'ticket_lang' => 'english',
                            'comp_name_font' => $this->input->post('com_font_e'),
                            'comp_name_aligment' => $this->input->post('com_ali_e'),
                            'date_font' => $this->input->post('date_font_e'),
                            'date_aligment' => $this->input->post('date_ali_e'),
                            'expected_time_font' => $this->input->post('expec_font_e'),
                            'expected_time_aligment' => $this->input->post('expec_ali_e'),
                            'terminal_id_font' => $this->input->post('ter_font_e'),
                            'terminal_id_aligment' => $this->input->post('ter_ali_e'),
                            'service_name_font' => $this->input->post('ser_font_e'),
                            'service_name_aligment' => $this->input->post('ser_ali_e'),
                            'branch_name_font' => $this->input->post('branch_font_e'),
                            'branch_name_aligment' => $this->input->post('branch_ali_e'),
                            'time_font' => $this->input->post('time_font_e'),
                            'time_aligment' => $this->input->post('time_ali_e'),
                            'ticket_num_font' => $this->input->post('ticket_font_e'),
                            'ticket_num_aligment' => $this->input->post('ticket_ali_e'),
                            'waiting_customers_font' => $this->input->post('cus_num_font_e'),
                            'waiting_customers_aligment' => $this->input->post('cus_num_ali_e'),
                            'ticket_img_width' => $this->input->post('tic_img_width_e'),
                            'ticket_img_height' => $this->input->post('tic_img_height_e'),
                            'tabs_before_img' => $this->input->post('tic_img_tabs_e'),
                            'comp_name_font_size' => $this->input->post('comp_name_font_size_e'),
                            'date_font_size' => $this->input->post('date_font_size_e'),
                            'expected_time_font_size' => $this->input->post('expected_time_font_size_e'),
                            'terminal_id_font_size' => !empty($this->input->post('terminal_id_font_size_e')) ? $this->input->post('terminal_id_font_size_e') : 0,
                            'service_name_font_size' => $this->input->post('service_name_font_size_e'),
                            'branch_name_font_size' => $this->input->post('branch_name_font_size_e'),
                            'time_font_size' => $this->input->post('time_font_size_e'),
                            'ticket_num_font_size' => $this->input->post('ticket_num_font_size_e'),
                            'waiting_customers_font_size' => $this->input->post('waiting_customers_font_size_e'),
                            'footer_ad_font_size' => $this->input->post('footer_ad_font_size_e'),
                            'footer_ad_font' => $this->input->post('footer_ad_font_e'),
                            'footer_ad_aligment' => $this->input->post('footer_ad_ali_e'),
                            'header_ad_font_size' => $this->input->post('header_ad_font_size_e'),
                            'header_ad_font' => $this->input->post('header_ad_font_e'),
                            'header_ad_aligment' => $this->input->post('header_ad_ali_e'),
                            'logo_ali' => $this->input->post('logo_ali_e'),
                            'show_comp_n_block' => $this->input->post('show_comp_n_block_e') ? 1 : 0,
                            'show_date_block' => $this->input->post('show_date_block_e') ? 1 : 0,
                            'show_exp_time_block' => $this->input->post('show_exp_time_block_e') ? 1 : 0,
                            'show_service_n_block' => $this->input->post('show_service_n_block_e') ? 1 : 0,
                            'show_ticket_img_block' => $this->input->post('show_ticket_img_block_e') ? 1 : 0,
                            'show_branch_n_block' => $this->input->post('show_branch_n_block_e') ? 1 : 0,
                            'show_time_block' => $this->input->post('show_time_block_e') ? 1 : 0,
                            'show_ticket_num_block' => $this->input->post('show_ticket_num_block_e') ? 1 : 0,
                            'show_wait_cust_num_block' => $this->input->post('show_wait_cust_num_block_e') ? 1 : 0,
                            'show_footer_block' => $this->input->post('show_footer_block_e') ? 1 : 0,
                            'show_header_block' => $this->input->post('show_header_block_e') ? 1 : 0,
                        );
                        if ($_FILES['header_img_e']['name'] != '') {
                            $config['upload_path'] = 'uploads/';
                            $config['allowed_types'] = 'gif|jpg|png|jpeg|bmp';
                            $this->load->library('upload', $config);
                            if (!$this->upload->do_upload('header_img_e') && $_FILES['header_img_e']['error'] != 4) { //Error Occured
                                echo '<br/> Ticket Image: ' . $this->upload->display_errors() . '<br/><a href="' . base_url() . 'C_templates/template_general_settings/' . $temp_id . '/#tab1_5" >Back</a>';
                                die();
                            } else {
                                $data_e['ticket_image'] = $this->upload->data()['file_name'];
                            }
                        }
                        $this->db->select('id');
                        $row = $this->db->get_where('vr_ticket_sett', array('template_id' => $data_e['template_id'], 'ticket_lang' => 'english'))->row();
                        if (!check_is_server()) {
                            $data_e['edited_on_client'] = 1;
                        }
                        if ($row) {
                            $this->db->where('id', $row->id);
                            $this->db->update('vr_ticket_sett', $data_e);
                            $insetrted_id = $row->id;
                        } else {
                            $this->db->insert('vr_ticket_sett', $data_e);
                            $insetrted_id = $this->db->insert_id();
                        }
                        if ($this->db->dbdriver == 'sqlsrv') {
                            $this->db->query('UPDATE "vr_ticket_sett" SET "ar_footer_text" = N\'' . $this->input->post('ar_foot_text') . '\' , "sms_ar_text" = N\'' . $this->input->post('sms_ar_text') . '\' WHERE "id" = ' . $insetrted_id);
                        } else {
                            $this->db->query('UPDATE vr_ticket_sett SET ar_footer_text = \'' . $this->input->post('ar_foot_text') . '\' , sms_ar_text = \'' . $this->input->post('sms_ar_text') . '\' WHERE id = ' . $insetrted_id);
                        }
                    }
                    update_branch_sett('vr_ticket_sett', $temp_id);
                    record_log('Adding standalone ticket settings');
                    redirect(base_url() . 'C_templates/template_ticket_settings/' . $temp_id . '/success');
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function check_add_temp_terminal()
    {
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('ter_no', $this->lang->line('terminal_num'), 'required|integer');
            $this->form_validation->set_rules('display_address', $this->lang->line('display_add'), 'required|integer');
            if ($this->form_validation->run() == false) {
                echo validation_errors();
            } else {
                echo 'yes';
            }
        }
    }

    public function check_edit_temp_terminal()
    {
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('edit_ter_num', $this->lang->line('terminal_num'), 'required|integer');
            $this->form_validation->set_rules('dis_add_edit', $this->lang->line('display_add'), 'required|integer');
            if ($this->form_validation->run() == false) {
                echo validation_errors();
            } else {
                echo 'yes';
            }
        }
    }

    public function save_temp_terminal_config($temp_id)
    {
        if ($this->session->userdata('login')) {
            $dis_active = 0;
            $dis_comm = 0;
            $ter_comm = 0;
            if ($this->input->post('dis_active') != null) {
                $dis_active = 1;
            }
            if ($this->input->post('dis_comm') != null) {
                $dis_comm = 1;
            }
            if ($this->input->post('term_comm') != null) {
                $ter_comm = 1;
            }
            $data = array(
                'terminal_num' => $this->input->post('ter_no'),
                'display_address' => $this->input->post('display_address'),
                'display_active' => $dis_active,
                'display_communication' => $dis_comm,
                'terminal_communication' => $ter_comm,
                'direction' => $this->input->post('dir'),
                'temp_id' => $temp_id,
            );
            $inserted_id = $this->m_templates->save_temp_terminal($data, $temp_id);
            $this->m_templates->save_terminal_services($this->input->post('assigned_services'), $this->input->post('assigned_prio'), $inserted_id, $temp_id);
            record_log('Adding template terminal configurations');
            redirect(base_url() . 'C_templates/template_general_settings/' . $temp_id . '/success/#tab1_6');
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function show_terminal_services($temp_terminal_id)
    {
        if ($this->session->userdata('login')) {
            $arr['active'] = 'temp';
            $arr['data'] = $this->m_templates->get_terminal_services($temp_terminal_id);
            $arr['temp_terminal_id'] = $temp_terminal_id;
            CI_load_view::load_view('template/template_terminal_services', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function assign_service_terminal($temp_terminal_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("assign_service_terminal_lvl", "v")) {
                $arr['services'] = $this->m_templates->get_services_for_terminal($temp_terminal_id);
                $arr['priorities'] = $this->m_templates->get_priorities();
                $arr['temp_terminal_id'] = $temp_terminal_id;
                $arr['active'] = 'temp';
                CI_load_view::load_view('template/template_terminal_services_form', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function save_service_to_terminal($temp_terminal_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("assign_service_terminal_lvl", "c")) {
                $data = array(
                    'service_id' => $this->input->post('service_name'),
                    'priority_id' => $this->input->post('pr_name'),
                    'template_terminal_id' => $temp_terminal_id,
                );
                $this->m_templates->insert_service_in_terminal($data);
                record_log('Adding service to terminal');
                redirect(base_url() . 'C_templates/show_terminal_services/' . $temp_terminal_id . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function edit_assign_service_terminal($id, $temp_terminal_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("assign_service_terminal_lvl", "u")) {
                $arr['data'] = $this->m_templates->get_terminal_service_assign_with_id($id); //get service name & priority for this id
                //as assign_service_terminal function .....................
                $arr['services'] = $this->m_templates->get_services_for_terminal($temp_terminal_id);
                $arr['priorities'] = $this->m_templates->get_priorities();
                $arr['temp_terminal_id'] = $temp_terminal_id;
                $arr['active'] = 'temp';
                CI_load_view::load_view('template/template_terminal_services_form', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function edit_data_service_terminal($service_ter_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("assign_service_terminal_lvl", "u")) {
                $data = array(
                    'service_id' => $this->input->post('service_name'),
                    'priority_id' => $this->input->post('pr_name'),
                );
                $this->m_templates->edit_terminal_service_assign($data, $service_ter_id);
                record_log('Update data service terminal :id ' . $service_ter_id);
                redirect(base_url() . 'C_templates/show_terminal_services/' . $this->input->post('temp_terminal_id') . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function unassign_service_terminal($id, $temp_terminal_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("assign_service_terminal_lvl", "d")) {
                $this->db->delete('template_terminal_services', array('id' => $id));
                record_log('Delete template terminal service :id ' . $id);
                redirect(base_url() . 'C_templates/show_terminal_services/' . $temp_terminal_id . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_terminal_info($temp_terminal_id)
    {
        if ($this->session->userdata('login')) {
            echo json_encode($this->db->get_where('template_terminals', array('id' => $temp_terminal_id))->row());
        }
    }

    public function save_edit_temp_terminal($temp_terminal_id, $temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("assign_service_terminal_lvl", "u")) {
                $dis_active = 0;
                $dis_comm = 0;
                $ter_comm = 0;
                if ($this->input->post('dis_active_edit') != null) {
                    $dis_active = 1;
                }
                if ($this->input->post('dis_comm_edit') != null) {
                    $dis_comm = 1;
                }
                if ($this->input->post('ter_comm_edit') != null) {
                    $ter_comm = 1;
                }
                $data = array(
                    'terminal_num' => $this->input->post('edit_ter_num'),
                    'display_address' => $this->input->post('dis_add_edit'),
                    'display_active' => $dis_active,
                    'display_communication' => $dis_comm,
                    'terminal_communication' => $ter_comm,
                );
                $this->m_templates->update_terminal($data, $temp_terminal_id, $temp_id);
                record_log('Update template terminal  :id ' . $temp_terminal_id);
                redirect(base_url() . 'C_templates/template_general_settings/' . $temp_id . '/success/#tab1_6');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete_template($id, $temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("assign_service_terminal_lvl", "d")) {
                $this->db->delete('template_terminals', array('id' => $id));
                $this->m_templates->insert_operation_on_temp('update', $temp_id);
                record_log('Delete template terminal  :id ' . $id);
                redirect(base_url() . 'C_templates/template_general_settings/' . $temp_id . '/success/#tab1_6');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    //==============================================  Hardware Settings ==============================================
    public function template_hardware_settings($temp_id, $success = null)
    {
        if ($this->session->userdata('login')) {
            if (or_pb(["hardware_request_lvl", "hw_sett_lvl", "hw_addresses_lvl", "jumbo_display_sett_lvl", "hw_services_lvl"])) {
                if (!$this->m_templates->branch_deleted_or_not_exist($temp_id)) {
                    $this->load->model('M_general');
                    $user_brnches = $this->M_general->get_user_branches();
                    if (!in_array($temp_id, $user_brnches)) {
                        $error['text'] = $this->lang->line('permission_to_branch');
                        $this->load->view('private/page-500', $error);
                        return;
                    }
                    $arr['temp_requests'] = $this->m_templates->get_temp_requests($temp_id);
                    $arr['temp_data'] = $this->m_templates->get_branch_data($temp_id);
                    $arr['curr_req'] = $this->m_templates->get_current_reqs($temp_id);
                    $arr['req_unit_ver'] = $this->m_templates->get_req_unit_version($temp_id);
                    $arr['hardware_sett'] = $this->m_templates->get_hw_hardware_settings($temp_id);
                    $arr['address_sett'] = $this->m_templates->get_hw_address_settings($temp_id);
                    $arr['jumbo_displays'] = $this->m_templates->get_temp_jumbo_displays($temp_id);
                    $arr['template_services'] = $this->m_templates->get_template_services_unsetted($temp_id);
                    $arr['services'] = $this->m_templates->get_hw_services($temp_id);
                    $arr['active'] = 'hw_settings';
                    if ($success != null) {
                        $arr['success'] = 'success';
                    }
                    CI_load_view::load_view('template/template_hw_sett', $arr);
                } else {
                    $error['text'] = $this->lang->line('branch_not_found');
                    $this->load->view('private/page-500', $error);
                    return;
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function list_all_branches($link)
    {
        if ($this->session->userdata('login')) {
            $arr['branches'] = $this->m_templates->get_branches();
        } else {
            $arr['branches'] = $this->m_templates->get_branches_without_logging();
        }
        if ($link == 0) {
            if (or_pb(["hardware_request_lvl", "hw_sett_lvl", "hw_addresses_lvl", "jumbo_display_sett_lvl", "hw_services_lvl"])) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_templates/template_hardware_settings/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'C_templates/template_hardware_settings';
                $arr['active'] = 'hw_settings';
            }
        } elseif ($link == 1) {
            if (get_p("tic_template_sett_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_templates/template_ticket_settings/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'C_templates/template_ticket_settings';
                $arr['active'] = 'b_tic_sett';
            }
        } elseif ($link == 2) {
            if (get_p("tic_display_sett_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_templates/template_vr_displays/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'C_templates/template_vr_displays';
                $arr['active'] = 'b_vr_sett';
            }
        } elseif ($link == 3) {
            if (get_p("service_escalation_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_service/service_escalation/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'C_service/service_escalation';
                $arr['active'] = 'service_escalation';
            }
        } elseif ($link == 4) {
            if ($this->session->userdata('branch_id') != 0) {
                redirect(base_url() . 'C_templates/vr_services/' . $this->session->userdata('branch_id'));
            }
            $arr['link'] = 'C_templates/vr_services';
            $arr['active'] = 'manage_service';
            $arr['reservations'] = 1;
        } elseif ($link == 5) {
            if (get_p("advanced_settings_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_templates/advanced_settings/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'C_templates/advanced_settings';
                $arr['active'] = 'advanced';
            }
        } elseif ($link == 6) {
            if (get_p("tic_template_sett_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_templates/general_ticket_settings/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'C_templates/general_ticket_settings';
                $arr['active'] = 'general_ticket';
            }
        } elseif ($link == 7) {
            if (get_p("terminal_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_terminal/loadShow/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'C_terminal/loadShow';
                $arr['active'] = 'terminal';
            }
        } elseif ($link == 8) {
            if ($this->session->userdata('branch_id') != 0) {
                redirect(base_url() . 'C_settings/index/' . $this->session->userdata('branch_id'));
            }
            $arr['link'] = 'C_settings/index';
            $arr['active'] = 'v_r_s';
        } elseif ($link == 9) {
            if (get_p("workflows_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_workflow/workflows');
                }
                $arr['link'] = 'C_workflow/workflows';
                $arr['active'] = 'workflows';
            }
        } elseif ($link == 10) {
            if (get_p("workstations_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'Counter/workstations/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'Counter/workstations';
                $arr['active'] = 'workstation_layout';
            }
        } elseif ($link == 11) {
            if (get_p("waiting_areas_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_templates/waiting_areas/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'C_templates/waiting_areas';
                $arr['active'] = 'waiting_area_layout';
            }
        } elseif ($link == 12) {
            if (isset($this->session->userdata("license")['lcd-based_waiting_area_display']) && $this->session->userdata("license")['lcd-based_waiting_area_display']['open'] == 1 && (check_is_standalone() || !check_is_server())) {
                if (get_p("waiting_area_display_lvl", "v")) {
                    if ($this->session->userdata('branch_id') != 0) {
                        redirect(base_url() . 'C_terminal/select_waiting_area');
                    }
                    $arr['link'] = 'C_terminal/select_waiting_area';
                    $arr['active'] = 'waiting_area_display';
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } elseif ($link == 13) {
            if (get_p("agents_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_users/show_branch_agents/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'C_users/show_branch_agents';
                $arr['active'] = 'agent';
            }
        } elseif ($link == 14) {
            if (get_p("qvrequest_display_lvl", "v") && (check_is_standalone() || !check_is_server())) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_templates/qvrequest_display');
                } else {
                    redirect(base_url() . 'C_login/select_access_modal');
                }
                $arr['link'] = 'C_templates/qvrequest_display';
                $arr['active'] = 'qvrequest_display';
            }
        } elseif ($link == 15) {
            if (get_p("show_client_workflow_lvl", "v") && (check_is_standalone() || !check_is_server())) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_workflow/show_client_workflow');
                }
                $arr['link'] = 'C_workflow/show_client_workflow';
                $arr['active'] = 'show_client_workflow';
            }
        } elseif ($link == 16) {
            if ((check_is_server() || check_is_standalone()) && get_p("reservation_import_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_settings/reservation_import');
                }
                $arr['link'] = 'C_settings/reservation_import';
                $arr['active'] = 'reservation_sett';
            }
        } elseif ($link == 17) {
            $arr['link'] = 'C_terminal/select_waiting_area';
            $arr['active'] = 'reservation_sett';
        } elseif ($link == 18) {
            if (get_p("workflows_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_workflow/reset_tickets');
                }
                $arr['link'] = 'C_workflow/reset_tickets';
                $arr['active'] = '';
            }
        } elseif ($link == 19) {
            if (isset($this->session->userdata("license")['lcd-based_workstation_display']) && $this->session->userdata("license")['lcd-based_workstation_display']['open'] == 1 && $this->session->userdata('User_type') == 'Agent') {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_terminal/select_workstation');
                }
                $arr['link'] = 'C_terminal/select_workstation';
                $arr['active'] = 'waiting_area_display';
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } elseif ($link == 20) {
            if (get_p("internal_area_lvl", "v")) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_terminal/loadInternalAreas/' . $this->session->userdata('branch_id'));
                }
                $arr['link'] = 'C_terminal/loadShow';
                $arr['active'] = 'internal_areas';
            }
        } elseif ($link == 21) {
            if (get_p("reprint_ticket_lvl", "v") && (check_is_standalone() || !check_is_server())) {
                if ($this->session->userdata('branch_id') != 0) {
                    redirect(base_url() . 'C_workflow/reprint_ticket');
                }
                $arr['link'] = 'C_workflow/reprint_ticket';
                $arr['active'] = 'reprint_ticket';
            }
        }
        CI_load_view::load_view('template/list_all_branches', $arr);
    }

    public function template_ticket_settings($temp_id, $status = null, $errors = null)
    {
        if ($this->session->userdata('login')) {
            if (get_p("tic_template_sett_lvl", "v")) {
                $this->load->model('M_general');
                $user_brnches = $this->M_general->get_user_branches();
                if (!in_array($temp_id, $user_brnches)) {
                    $error['text'] = $this->lang->line('permission_to_branch');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['template_settings_e'] = $this->m_templates->get_vr_tic_sett($temp_id, 'english');
                $arr['template_settings'] = $this->m_templates->get_vr_tic_sett($temp_id, 'arabic');
                $arr['active'] = 'b_tic_sett';
                $arr['branch_id'] = $temp_id;
                $arr['branch_name'] = $this->m_templates->get_branch_name($temp_id);
                if (!$arr['branch_name']) {
                    $error['text'] = $this->lang->line('branch_not_found');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                if ($status != null) {
                    $arr['status'] = $status;
                }
                if ($errors != null) {
                    $arr['error'] = $errors;
                }
                CI_load_view::load_view('branch/ticket_sett', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function template_vr_displays($temp_id, $status = null)
    {
        if ($this->session->userdata('login')) {
            if (get_p("tic_display_sett_lvl", "v")) {
                $this->db->select('id,title');
                $arr['displays'] = $this->db->get_where('vr_display_sett', array('template_id' => $temp_id))->result();
                if ($status != null) {
                    $arr['status'] = $status;
                }
                $arr['branch_id'] = $temp_id;
                $arr['active'] = 'b_vr_sett';
                CI_load_view::load_view('branch/vr_displays', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function template_vr_settings($temp_id, $id = 0)
    {
        if ($this->session->userdata('login')) {
            if (get_p("tic_display_sett_lvl", "c") || get_p("tic_display_sett_lvl", "u")) {
                $this->load->model('M_general');
                $user_brnches = $this->M_general->get_user_branches();
                if (!in_array($temp_id, $user_brnches)) {
                    $error['text'] = $this->lang->line('permission_to_branch');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['template_settings'] = $this->m_templates->get_vr_display_sett($id);
                $arr['display_workflow'] = $this->m_templates->get_display_workflows($id);
                $arr['active'] = 'b_vr_sett';
                $arr['branch_id'] = $temp_id;
                $arr['id'] = $id;
                $arr['branch_name'] = $this->m_templates->get_branch_name($temp_id);
                if (!$arr['branch_name']) {
                    $error['text'] = $this->lang->line('branch_not_found');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                if ($status != null) {
                    $arr['status'] = $status;
                }
                $arr['workflows'] = $this->m_templates->get_branch_workflows($temp_id);
                CI_load_view::load_view('branch/vr_display_sett', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

     public function template_vr_workflow_settings($temp_id, $id = 0)
    {
        if ($this->session->userdata('login')) {
            if (get_p("tic_display_sett_lvl", "c") || get_p("tic_display_sett_lvl", "u")) {
                $this->load->model('M_general');
                $user_brnches = $this->M_general->get_user_branches();
                if (!in_array($temp_id, $user_brnches)) {
                    $error['text'] = $this->lang->line('permission_to_branch');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['template_settings'] = $this->m_templates->get_vr_display_sett($id);
                $arr['display_workflow'] = $this->m_templates->get_display_workflows($id);
                $arr['display_services'] = $this->m_templates->get_display_workflows_services($arr['display_workflow']);
                $arr['display_services_agents'] = $this->m_templates->get_display_services_agents($arr['display_services']['unique_service_ids']);
				$this->load->model('m_users');
				$arr['agents'] = $this->m_users->get_branch_agents($temp_id);
                $arr['active'] = 'b_vr_sett';
                $arr['branch_id'] = $temp_id;
                $arr['id'] = $id;
                $arr['branch_name'] = $this->m_templates->get_branch_name($temp_id);
                if (!$arr['branch_name']) {
                    $error['text'] = $this->lang->line('branch_not_found');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                if ($status != null) {
                    $arr['status'] = $status;
                }
                $arr['workflows'] = $this->m_templates->get_branch_workflows($temp_id);
                CI_load_view::load_view('branch/vr_display_sett_workflow', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function add_temp_request($temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("hardware_request_lvl", "c")) {
                $data = array(
                    'request_id' => $this->input->post('req_id'),
                    'request_address' => $this->input->post('req_add'),
                    'temp_id' => $temp_id,
                );
                $this->m_templates->insert_temp_request($data);
                update_branch_sett('hw_requests', $temp_id);
                record_log('Adding template request  ');
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_request_data($req_id)
    {
        if ($this->session->userdata('login')) {
            echo json_encode($this->m_templates->get_req_data($req_id));
        }
    }

    public function save_edit_req($req_id, $temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("hardware_request_lvl", "u")) {
                $data = array(
                    'request_id' => $this->input->post('req_id_edit'),
                    'request_address' => $this->input->post('req_add_edit'),
                );
                $this->m_templates->save_update_request($data, $req_id, $temp_id);
                update_branch_sett('hw_requests', $temp_id);
                record_log('Update template request  :id ' . $req_id);
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete_temp_request($id, $temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("hardware_request_lvl", "d")) {
                $this->db->delete('temp_requests', array('id' => $id));
                update_branch_sett('hw_requests', $temp_id);
                record_log('Delete template request  :id ' . $id);
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_standard_req_id($temp_id)
    {
        if ($this->session->userdata('login')) {
            echo json_encode($this->m_templates->get_standard_req_id($temp_id));
        }
    }

    public function save_curr_req($temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("hw_sett_lvl", "u")) {
                $curr_reqs = $this->input->post('curr_reqs');
                if (!empty($curr_reqs)) {
                    $curr_req = implode(',', $this->input->post('curr_reqs'));
                } else {
                    $curr_req = '';
                }
                $data = array(
                    'req_unit_ver' => $this->input->post('req_unit_version'),
                    'curr_reqs' => $curr_req,
                    'temp_id' => $temp_id,
                );
                update_branch_sett('hw_general_sett', $temp_id);
                $this->m_templates->insert_current_request($data, $temp_id);
                record_log('Adding template request ');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function check_save_hardware_sett()
    {
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('wam_port', 'Wam COM Port', 'integer');
            $this->form_validation->set_rules('busy_time', 'Busy Time', 'integer');
            if ($this->input->post('printer') != null) {
                $this->form_validation->set_rules('printer_port', 'Printer COM Port', 'required|integer');
            }
            if ($this->form_validation->run() == false) {
                echo validation_errors();
            } else {
                echo 'yes';
            }
        }
    }

    public function save_hardware_sett($temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("hw_sett_lvl", "u")) {
                $data = array(
                    'temp_id' => $temp_id,
                    'wam_com_port' => $this->input->post('wam_port') == '' ? null : $this->input->post('wam_port'),
                    'clock_mode' => $this->input->post('clock_mode'),
                    'enable_send' => $this->input->post('send') == null ? 0 : 1,
                    'enable_sound' => $this->input->post('sound') == null ? 0 : 1,
                    'enable_active' => $this->input->post('active') == null ? 0 : 1,
                    'enable_password' => $this->input->post('password') == null ? 0 : 1,
                    'operator_login' => $this->input->post('login') == null ? 0 : 1,
                    'save_trn' => $this->input->post('trn') == null ? 0 : 1,
                    'busy_time' => $this->input->post('busy_time') == '' ? null : $this->input->post('busy_time'),
                    'enable_printer' => $this->input->post('printer') == null ? 0 : 1,
                );
                if ($this->input->post('printer') != null) {
                    $data['printer_model'] = $this->input->post('printer_model');
                    $data['bound_rate'] = $this->input->post('bound_rate');
                    $data['printer_com_port'] = $this->input->post('printer_port');
                } else {
                    $data['printer_model'] = null;
                    $data['bound_rate'] = null;
                    $data['printer_com_port'] = null;
                }
                $this->m_templates->save_hw_settings('hw_hw_sett', $data);
                update_branch_sett('hw_hw_sett', $temp_id);
                record_log('save hardware setting');
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success/#tab1_1');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function save_addresses($temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("hw_addresses_lvl", "u")) {
                $data = array(
                    'temp_id' => $temp_id,
                    'window_address_start' => $this->input->post('win_add_start'),
                    'client_address_start' => $this->input->post('client_add_start'),
                    'jumbo_total_num' => $this->input->post('jumbos_totel_number'),
                    'client_display_add_start' => $this->input->post('dis_add_start'),
                    'total_terminals' => $this->input->post('total_terminals'),
                );
                $this->m_templates->save_hw_settings('hw_addresses_sett', $data);
                update_branch_sett('hw_address_sett', $temp_id);
                record_log('save hardware Addresses setting');
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success/#tab1_2');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function save_jumbo_display_sett($temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("jumbo_display_sett_lvl", "c")) {
                $win_status = 0;
                $client_status = 0;
                $clock = 0;
                if ($this->input->post('win_status') != null) {
                    $win_status = 1;
                }
                if ($this->input->post('client_status') != null) {
                    $client_status = 1;
                }
                if ($this->input->post('clock') != null) {
                    $clock = 1;
                }
                $data = array(
                    'win_address' => $this->input->post('win_add'),
                    'win_status' => $win_status,
                    'client_address' => $this->input->post('client_add'),
                    'client_status' => $client_status,
                    'display_mode' => $this->input->post('dis_mode'),
                    'no_of_boards' => $this->input->post('boards'),
                    'clock' => $clock,
                    'temp_id' => $temp_id,
                );
                if ($clock == 1) {
                    $data['always_on'] = $this->input->post('always_on');
                    $data['time_in'] = $this->input->post('time_in');
                    $data['time_out'] = $this->input->post('time_out');
                }
                $this->m_templates->save_jumbo_display_sett($data);
                update_branch_sett('hw_jumbo_display_sett', $temp_id);
                record_log('save jumbo display setting');
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success/#tab1_3');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_display_info($temp_display_id)
    {
        if ($this->session->userdata('login')) {
            echo json_encode($this->m_templates->get_temp_display_info($temp_display_id));
        }
    }

    public function save_edit_display($temp_display_id, $temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("jumbo_display_sett_lvl", "u")) {
                $win_status = 0;
                $client_status = 0;
                $clock = 0;
                if ($this->input->post('win_status_edit') != null) {
                    $win_status = 1;
                }
                if ($this->input->post('client_status_edit') != null) {
                    $client_status = 1;
                }
                if ($this->input->post('clock_edit') != null) {
                    $clock = 1;
                }
                $data = array(
                    'win_address' => $this->input->post('win_add_edit'),
                    'win_status' => $win_status,
                    'client_address' => $this->input->post('client_add_edit'),
                    'client_status' => $client_status,
                    'display_mode' => $this->input->post('dis_mode_edit'),
                    'no_of_boards' => $this->input->post('boards_edit'),
                    'clock' => $clock,
                    'temp_id' => $temp_id,
                );
                if ($clock == 1) {
                    $data['always_on'] = $this->input->post('always_on_edit');
                    $data['time_in'] = $this->input->post('time_in_edit');
                    $data['time_out'] = $this->input->post('time_out_edit');
                } else {
                    $data['always_on'] = null;
                    $data['time_in'] = null;
                    $data['time_out'] = null;
                }
                $this->m_templates->update_jumbo_display_sett($data, $temp_display_id, $temp_id);
                update_branch_sett('hw_jumbo_display_sett', $temp_id);
                record_log('Update jumbo display setting :id ' . $temp_display_id);
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success/#tab1_3');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete_jumbo_display($id, $temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("jumbo_display_sett_lvl", "d")) {
                $this->db->delete('template_jumbo_displays', array('id' => $id));
                update_branch_sett('hw_jumbo_display_sett', $temp_id);
                record_log('Delete jumbo display setting :id ' . $id);
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success/#tab1_3');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function check_redundant_window_client($temp_id)
    {
        if ($this->session->userdata('login')) {
            if (!$this->m_templates->check_redundant_window_address($this->input->post('win_add'), $temp_id)) {
                if (!$this->m_templates->check_redundant_window_client($this->input->post('client_add'), $temp_id)) {
                    echo 'yes';
                } else {
                    echo $this->lang->line('client_add_exists_error');
                }
            } else {
                echo $this->lang->line('win_add_exists_error');
            }
        }
    }

    public function check_redundant_window_client_for_edit($temp_id)
    {
        if ($this->session->userdata('login')) {
            if (!$this->m_templates->check_redundant_window_address_edit($this->input->post('win_add_edit'), $this->input->post('display_id'), $temp_id)) {
                if (!$this->m_templates->check_redundant_window_client_edit($this->input->post('client_add_edit'), $this->input->post('display_id'), $temp_id)) {
                    echo 'yes';
                } else {
                    echo $this->lang->line('client_add_exists_error');
                }
            } else {
                echo $this->lang->line('win_add_exists_error');
            }
        }
    }

    public function check_add_hw_service()
    {
        if ($this->session->userdata('login')) {
            $this->form_validation->set_rules('sound_zone', 'Sound Zone', 'required|integer|less_than[256]');
            if ($this->form_validation->run() == false) {
                echo validation_errors();
            } else {
                $seleced_displays = $this->input->post('selected_displays');
                if ($this->input->post('selected_displays') != null && !empty($seleced_displays)) {
                    echo 'yes';
                } else {
                    echo $this->lang->line('jumbo_displays_error');
                }
            }
        }
    }

    public function save_hardware_service($temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("hw_services_lvl", "c")) {
                $data = array(
                    'service' => $this->input->post('hw_services'),
                    'sound_zones' => $this->input->post('sound_zone'),
                    'jumbo_displays' => implode(',', $this->input->post('selected_displays')),
                    'temp_id' => $temp_id,
                );
                $this->m_templates->add_hw_service($data);
                update_branch_sett('hw_services_sett', $temp_id);
                record_log('Adding hardware_service ');
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success/#tab1_4');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_hw_service_data_by_id($hw_service_id)
    {
        if ($this->session->userdata('login')) {
            echo json_encode($this->m_templates->get_hw_service_data_by_id($hw_service_id));
        }
    }

    public function edit_hw_services_page($temp_id, $hw_service_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("hw_services_lvl", "u")) {
                if (!$this->m_templates->branch_deleted_or_not_exist($temp_id)) {
                    $arr['service_data'] = $this->m_templates->get_hw_service_by_id($hw_service_id);
                    if (!$arr['service_data']) {
                        $error['text'] = 'This Service Not Found';
                        $this->load->view('private/page-500', $error);
                        return;
                    }
                    $arr['template_services'] = $this->m_templates->get_template_services_unsetted($temp_id);
                    $arr['dislays'] = $this->m_templates->get_temp_jumbo_displays($temp_id);
                    $arr['temp_id'] = $temp_id;
                    $arr['hw_service_id'] = $hw_service_id;
                    $arr['active'] = 'hw_settings';
                    CI_load_view::load_view('template/edit_hw_services', $arr);
                } else {
                    $error['text'] = $this->lang->line('branch_not_found');
                    $this->load->view('private/page-500', $error);
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function save_edit_hw_service($temp_id, $hw_service_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("hw_services_lvl", "u")) {
                $data = array(
                    'service' => $this->input->post('hw_services'),
                    'sound_zones' => $this->input->post('sound_zone'),
                    'jumbo_displays' => implode(',', $this->input->post('selected_displays')),
                    'temp_id' => $temp_id,
                );
                $this->m_templates->update_hw_service($data, $hw_service_id);
                update_branch_sett('hw_services_sett', $temp_id);
                record_log('Update hardware_service :id ' . $hw_service_id);
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success/#tab1_4');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete_hw_service($id, $temp_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("hw_services_lvl", "d")) {
                $this->db->delete('temp_hw_services', array('id' => $id));
                update_branch_sett('hw_services_sett', $temp_id);
                record_log('Delete hardware_service :id ' . $id);
                redirect(base_url() . 'C_templates/template_hardware_settings/' . $temp_id . '/success/#tab1_4');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function workstation_layout_settings($branch_id, $status = null)
    {
        if ($this->session->userdata('login')) {
            if (get_p("workstations_lvl", "v")) {
                $this->load->model('M_general');
                $user_brnches = $this->M_general->get_user_branches();
                if (!in_array($branch_id, $user_brnches)) {
                    $error['text'] = $this->lang->line('permission_to_branch');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                $arr['workstation_layout_sett'] = $this->m_templates->get_workstation_layout_sett($branch_id);
                $arr['slideshow_photos'] = $this->m_templates->slideshow_workstation_photos($branch_id);
                $arr['active'] = 'workstation_layout';
                $arr['branch_id'] = $branch_id;
                $arr['branch_name'] = $this->m_templates->get_branch_name($branch_id);
                if (!$arr['branch_name']) {
                    $error['text'] = $this->lang->line('branch_not_found');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                if ($status != null) {
                    $arr['status'] = $status;
                }
                CI_load_view::load_view('branch/workstation_layout', $arr);
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function save_workstation_layout_settings($branch_id)
    {
        if (check_is_server() && get_p("workstations_lvl", "u")) {
            $settings = $this->m_templates->get_workstation_layout_sett($branch_id);
            if (!is_numeric($this->input->post('refresh_rate'))) {
                $error['text'] = $this->lang->line('refresh_rate_not_numeric');
                $this->load->view('private/page-500', $error);
                return;
            }
            $data = array(
                'branch_id' => $branch_id,
                'logo_alignment' => $this->input->post('logo_alignment'),
                'background_color' => $this->input->post('back_color'),
                'terminal_font' => $this->input->post('terminal_font'),
                'terminal_font_size' => $this->input->post('terminal_font_size'),
                'terminal_font_color' => $this->input->post('terminal_font_color'),
                'terminal_margin_top' => $this->input->post('terminal_margin_top'),
                'terminal_alignment' => $this->input->post('terminal_alignment'),
                'ticket_num_font' => $this->input->post('ticket_num_font'),
                'ticket_num_font_size' => $this->input->post('ticket_num_font_size'),
                'ticket_num_font_color' => $this->input->post('ticket_num_font_color'),
                'ticket_num_margin_top' => $this->input->post('ticket_num_margin_top'),
                'ticket_alignment' => $this->input->post('ticket_alignment'),
                'refresh_rate' => $this->input->post('refresh_rate'),
                'idle_sett' => $this->input->post('idle_sett'),
                'html_control' => $this->input->post('html_control'),
            );
            $error = '';
            // BEGIN FILE UPLOADS
            if ($_FILES['ws_logo']['name'] != '') {
                $config['upload_path'] = './uploads/layouts/workstation';
                $config['allowed_types'] = 'gif|jpg|png|jpeg';
                $this->load->library('upload', $config);
                if (!$this->upload->do_upload('ws_logo')) {
                    $error = 'Logo : ' . $this->upload->display_errors();
                } else {
                    $data['logo'] = $this->upload->data()['file_name'];
                }
            }
            if ($_FILES['back_image']['name'] != '') {
                $config['upload_path'] = './uploads/layouts/workstation';
                $config['allowed_types'] = 'gif|jpg|png|jpeg';
                $this->load->library('upload', $config);
                if (!$this->upload->do_upload('back_image')) {
                    $error = 'Backgound image : ' . $this->upload->display_errors();
                } else {
                    $data['background_image'] = $this->upload->data()['file_name'];
                }
            }
            if ($this->input->post('movie_select') == 0) {
                // Direct URL
                $data['movie_type'] = 0;
                $data['movie_uploaded'] = null;
                $data['movie_url'] = $this->input->post('movie_url');
            } elseif ($this->input->post('movie_select') == 1) {
                // Uploaded Video
                $data['movie_type'] = 1;
                $data['movie_url'] = null;
                if ($_FILES['movie_uploaded_link']['name'] != '') {
                    $configVideo['upload_path'] = './uploads/layouts/workstation';
                    $configVideo['allowed_types'] = 'mp3|mp4|wmv|avi|flv';
                    $configVideo['max_size'] = '0';
                    $configVideo['remove_spaces'] = true;
                    $this->load->library('upload', $configVideo);
                    if (!$this->upload->do_upload('movie_uploaded_link')) {
                        //                        echo $this->upload->display_errors();
                        //                        die();
                        $error = $this->upload->display_errors();
                    } else {
                        $data['movie_uploaded'] = $this->upload->data()['file_name'];
                        update_branch_sett('movie_uploaded_workstation');
                    }
                }
            }
            if ($error != '') {
                $this->session->set_flashdata('error', $error);
                redirect(base_url("C_templates/workstation_layout_settings/$branch_id"));
            }
            // END FILE UPLOADS
            if (!empty($settings)) {
                if (!empty($data['background_image'])) {
                    unlink('./uploads/layouts/workstation/' . $settings->background_image);
                }
                if (!empty($data['logo'])) {
                    unlink('./uploads/layouts/workstation/' . $settings->logo);
                }
                if (!empty($data['movie_uploaded'])) {
                    unlink('./uploads/layouts/workstation/' . $settings->movie_uploaded);
                }
                $this->M_general->update('workstation_layout_sett', $settings->id, $data);
                $id = $settings->id;
                record_log('Update workstation layout settings');
            } else {
                $this->M_general->insert('workstation_layout_sett', $data);
                $id = $this->db->insert_id();
                record_log('Insert workstation layout settings');
            }
            // insert photos

            $data = array('type' => 0, 'work_waiting_id' => $id);
            for ($i = 1; $i <= $this->input->post('photos_count'); $i++) {
                if ($_FILES['photo_' . $i]['name'] != '') {
                    $config['upload_path'] = './uploads/layouts/workstation';
                    $config['allowed_types'] = 'gif|jpg|png|jpeg';
                    $this->load->library('upload', $config);
                    if (!$this->upload->do_upload('photo_' . $i)) {
                        $error = 'Slideshow photo : ' . $this->upload->display_errors();
                        break;
                    } else {
                        $data['photo'] = $this->upload->data()['file_name'];
                        $this->db->insert('slideshow_photos', $data);
                    }
                }
            }
            if ($error != '') {
                $this->session->set_flashdata('error', $error);
                redirect(base_url("C_templates/workstation_layout_settings/$branch_id"));
            }
            if (isset($data['photo'])) {
                update_branch_sett('slideshow_photo_uploaded_workstation');
            }
            //
            update_branch_sett('workstation_layout_sett', $branch_id);
            redirect(base_url("C_templates/workstation_layout_settings/$branch_id/success"));
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function waiting_area_layout_settings($branch_id, $status = null, $status_2 = null)
    {
        if ($this->session->userdata('login')) {
            if (isset($this->session->userdata("license")['number_of_supported_waiting_areas']) && $this->session->userdata("license")['number_of_supported_waiting_areas']['open'] == 1) {
                if (get_p("waiting_areas_lvl", "v")) {
                    $this->load->model('M_general');
                    $user_brnches = $this->M_general->get_user_branches();
                    if (!in_array($branch_id, $user_brnches)) {
                        $error['text'] = $this->lang->line('permission_to_branch');
                        $this->load->view('private/page-500', $error);
                        return;
                    }
                    if ($status != null) {
                        if (is_numeric($status)) {
                            $area_info = $this->m_templates->get_branch_area_id($branch_id, $status);
                            $area_id = $area_info ? $area_info->id : null;
                            if ($status_2 != null) {
                                $arr['status'] = $status_2;
                            }
                        } else {
                            $area_info = $this->m_templates->get_branch_area_id($branch_id);
                            $area_id = $area_info ? $area_info->id : null;
                            $arr['status'] = $status;
                        }
                    } else {
                        $area_info = $this->m_templates->get_branch_area_id($branch_id);
                        $area_id = $area_info ? $area_info->id : null;
                    }
                    $ignored_terminals = $this->m_templates->get_ignored_terminals($branch_id, $area_id);
                    $ignored_ids = [];
                    foreach ($ignored_terminals as $one) {
                        $ignored_ids[] = $one->terminal_id;
                    }
                    $arr['waiting_areas'] = $this->M_terminal->get_branch_waiting_areas($branch_id);
                    $arr['data'] = $this->M_terminal->my_get_all_data($branch_id, $ignored_ids);
                    $arr['slideshow_photos'] = $this->m_templates->slideshow_waitingarea_photos($area_id);
                    $arr['waiting_area_layout_sett'] = $this->m_templates->get_specific_waiting_area_layout_sett($branch_id, $area_id);
                    $arr['Area_terminals'] = $this->m_templates->get_specific_waiting_area_terminals($area_id);
                    $arr['active'] = 'waiting_area_layout';
                    $arr['area_id'] = $area_id;
                    $arr['branch_id'] = $branch_id;
                    $arr['branch_name'] = $this->m_templates->get_branch_name($branch_id);
                    if (!$arr['branch_name']) {
                        $error['text'] = $this->lang->line('branch_not_found');
                        $this->load->view('private/page-500', $error);
                        return;
                    }
                    CI_load_view::load_view('branch/waiting_area_layout', $arr);
                } else {
                    $error['text'] = $this->lang->line('permission_denied');
                    $this->load->view('private/page-500', $error);
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function Save_waiting_area_name($branch_id)
    {
        if ($this->input->post('waiting_area_name')):
            $data_waiting_area = $this->m_templates->save_new_working_area_name_data($this->input->post('waiting_area_name'), $branch_id);
        endif;
        if ($data_waiting_area):
            echo json_encode(['status' => 'yes', 'id' => $data_waiting_area]);
        else:
            echo json_encode(['status' => 'no']);
        endif;
    }

    public function save_waiting_area_layout_settings($branch_id)
    {
        if (get_p("waiting_areas_lvl", "u")) {
            if ($this->input->post('sequence_of_display') == 0) {
                $sequence_of_display = 0;
            } elseif ($this->input->post('sequence_of_display') == 1) {
                $sequence_of_display = 1;
            }
            $area_id = $this->input->post('selected_waiting_area');
            if (!is_numeric($this->input->post('refresh_rate')) || !is_numeric($this->input->post('rotation_rate'))) {
                redirect(base_url("C_templates/waiting_area_layout_settings/$branch_id/$area_id/fail"));
            }
            $data = array(
                'branch_id' => $branch_id,
                'logo_alignment' => $this->input->post('logo_alignment'),
                'background_color' => $this->input->post('back_color'),
                'terminal_font' => $this->input->post('terminal_font'),
                'terminal_font_size' => $this->input->post('terminal_font_size'),
                'terminal_font_color' => $this->input->post('terminal_font_color'),
                'ticket_num_font' => $this->input->post('ticket_num_font'),
                'ticket_num_font_size' => $this->input->post('ticket_num_font_size'),
                'ticket_num_font_color' => $this->input->post('ticket_num_font_color'),
                'sequence_of_display' => $sequence_of_display,
                'terminals_per_page' => $this->input->post('terminals_per_page'),
                'rotation_rate' => $this->input->post('rotation_rate'),
                'refresh_rate' => $this->input->post('refresh_rate'),
                'header_font' => $this->input->post('header_font'),
                'header_font_size' => $this->input->post('header_font_size'),
                'header_font_color' => $this->input->post('header_font_color'),
                'table_logo_dis' => $this->input->post('table_logo_dis'),
                'idle_sett' => $this->input->post('idle_sett'),
                'header_alignment' => $this->input->post('header_alignment'),
                'terminal_alignment' => $this->input->post('terminal_alignment'),
                'ticket_alignment' => $this->input->post('ticket_alignment'),
//                BEGIN NEWS TICKER
                'news_font' => $this->input->post("news_font"),
                'news_size' => $this->input->post("news_size"),
                'news_color' => $this->input->post("news_color"),
                'news_position' => $this->input->post("news_position"),
                'news_spacing' => $this->input->post("news_spacing"),
                'news_speed' => $this->input->post("news_speed"),
                'news_lang' => $this->input->post("news_lang"),
                'news_dir' => $this->input->post("news_dir"),
                'news_bg_color' => $this->input->post('news_bg_color'),
                'html_control' => $this->input->post('html_control'),
                'date_time_font_size' => $this->input->post('date_time_font_size'),
                'date_time_distance_before' => $this->input->post('date_time_distance_before'),
                'date_time_distance_after' => $this->input->post('date_time_distance_after'),
//                END NEWS TICKER
            );
            $data_term = $this->input->post('terminals_id');
            $data_waiting_area = [];
            if ($this->input->post('selected_waiting_area') && $this->input->post('waiting_area_id')) {
                $data_waiting_area[0] = $this->input->post('selected_waiting_area');
                for ($i = 1; $i <= count($this->input->post('waiting_area_id')); $i++) {
                    $data_waiting_area[] += $this->input->post('waiting_area_id')[$i - 1];
                }
            } elseif ($this->input->post('selected_waiting_area') || $this->input->post('waiting_area_id')) {
                if ($this->input->post('selected_waiting_area')) {
                    $data_waiting_area[] = $this->input->post('selected_waiting_area');
                } else {
                    for ($i = 0; $i < count($this->input->post('waiting_area_id')); $i++) {
                        $data_waiting_area[$i] = $this->input->post('waiting_area_id')[$i];
                    }
                }
            }
            // BEGIN FILE UPLOADS
            if ($_FILES['ws_logo']['name'] != '') {
                $config['upload_path'] = './uploads/layouts/waiting_area';
                $config['allowed_types'] = 'gif|jpg|png';
                $this->load->library('upload', $config);
                if (!$this->upload->do_upload('ws_logo')) {
                    $error = array('error' => $this->upload->display_errors());
                } else {
                    $data['logo'] = $this->upload->data()['file_name'];
                }
            }
            if ($_FILES['back_image']['name'] != '') {
                $config['upload_path'] = './uploads/layouts/waiting_area';
                $config['allowed_types'] = 'gif|jpg|png';
                $this->load->library('upload', $config);
                if (!$this->upload->do_upload('back_image')) {
                    $error = array('error' => $this->upload->display_errors());
                } else {
                    $data['background_image'] = $this->upload->data()['file_name'];
                }
            }
            if ($this->input->post('movie_select') == 0) {
                // Direct URL
                $data['movie_type'] = 0;
                $data['movie_uploaded'] = null;
                $data['movie_url'] = $this->input->post('movie_url');
            } elseif ($this->input->post('movie_select') == 1) {
                // Uploaded Video
                $data['movie_type'] = 1;
                $data['movie_url'] = null;
                if ($_FILES['movie_uploaded_link']['name'] != '') {
                    $configVideo['upload_path'] = './uploads/layouts/waiting_area';
                    $configVideo['allowed_types'] = 'mp3|mp4|wmv|avi|flv';
                    $configVideo['max_size'] = '0';
                    $configVideo['remove_spaces'] = true;
                    $this->load->library('upload', $configVideo);
                    if (!$this->upload->do_upload('movie_uploaded_link')) {
                        echo $this->upload->display_errors();
                        die();
                        $error = array('error' => $this->upload->display_errors());
                    } else {
                        $data['movie_uploaded'] = $this->upload->data()['file_name'];
                        update_branch_sett('movie_uploaded_waiting_area', $branch_id);
                    }
                }
            }
            // END FILE UPLOADS
            $photos_arr = array();

            for ($i = 0; $i < count($data_waiting_area); $i++) {
                $settings = $this->m_templates->get_specific_waiting_area_layout_sett($branch_id, $data_waiting_area[$i]);
                $terminals = $this->m_templates->get_specific_waiting_area_terminals($data_waiting_area[$i]);
                $id = $data_waiting_area[$i];
                if (!empty($settings)) {
                    if (!empty($data['background_image']) && $settings->background_image != null) {
                        unlink('./uploads/layouts/waiting_area/' . $settings->background_image);
                    }
                    if (!empty($data['logo']) && $settings->logo != null) {
                        unlink('./uploads/layouts/waiting_area/' . $settings->logo);
                    }
                    if (!empty($data['movie_uploaded']) && $settings->movie_uploaded != null) {
                        unlink('./uploads/layouts/waiting_area/' . $settings->movie_uploaded);
                    }
                    $this->M_general->update('waiting_area_layout_sett', $settings->id, $data);
                    record_log('Update waiting area layout settings');
                } else {
                    $data['waiting_area_id'] = $data_waiting_area[$i];
                    $this->M_general->insert('waiting_area_layout_sett', $data);
                    // $id = $this->db->insert_id();
                    record_log('Insert waiting area layout settings');
                }
                if (!empty($terminals)) {
                    $this->m_templates->delete_area_terminals($data_waiting_area[$i]);
                    for ($k = 0; $k < count($data_term); $k++) {
                        $terminal_key = $this->m_templates->get_terminal_key($data_term[$k]);
                        $this->M_general->insert('waiting_area_terminals', array('waiting_area_id' => $data_waiting_area[$i], 'terminal_id' => $data_term[$k], 'terminal_key' => $terminal_key));
                    }
                } else {
                    for ($k = 0; $k < count($data_term); $k++) {
                        $terminal_key = $this->m_templates->get_terminal_key($data_term[$k]);
                        $this->M_general->insert('waiting_area_terminals', array('waiting_area_id' => $data_waiting_area[$i], 'terminal_id' => $data_term[$k], 'terminal_key' => $terminal_key));
                    }
                }
                // start upload slideshow photos
                // first copy prev exists to new added waiting areas to copy settings or added now in prev upload step
                $prev_photos = $this->db->get_where('slideshow_photos', array('work_waiting_id' => $this->input->post('selected_waiting_area'), 'type' => 1))->result();

                foreach ($prev_photos as $photo) {
                    $copy_data = array('photo' => $photo->photo, 'type' => 1, 'work_waiting_id' => $id);
                    $exist_before = $this->db->get_where('slideshow_photos', $copy_data)->row();
                    if (!$exist_before) {
                        $this->db->insert('slideshow_photos', $copy_data);
                    }
                }
                // END
                // Start upload new imgs
                $data_photos = array('type' => 1, 'work_waiting_id' => $id);
                $error = '';
                if ($i == 0) { // upload imgs first time only to selected waiting area
                    for ($j = 1; $j <= $this->input->post('photos_count'); $j++) {
                        if ($_FILES['photo_' . $j]['name'] != '') {
                            $config['upload_path'] = './uploads/layouts/waiting_area';
                            $config['allowed_types'] = 'gif|jpg|png|jpeg';
                            $this->load->library('upload', $config);
                            if (!$this->upload->do_upload('photo_' . $j)) {
                                $error = 'Slideshow photo : ' . $this->upload->display_errors();
                                break;
                            } else {
                                $data_photos['photo'] = $this->upload->data()['file_name'];
                                // $photos_arr[] = $data_photos['photo'];
                                $this->db->insert('slideshow_photos', $data_photos);
                            }
                        }
                    }
                }
                if ($error != '') {
                    $this->session->set_flashdata('error', $error);
                    redirect(base_url("C_templates/waiting_area_layout_settings/$branch_id/$area_id"));
                }
                if (isset($data_photos['photo'])) {
                    update_branch_sett('slideshow_photo_waiting_area', $branch_id);
                }
                // END
            }

            update_branch_sett('waiting_area_layout_sett', $branch_id);
            redirect(base_url("C_templates/waiting_area_layout_settings/$branch_id/$area_id/success"));
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function unlink_workstation_images($branch_id, $flag)
    {
        if (get_p("workstations_lvl", "u")) {
            $settings = $this->m_templates->get_workstation_layout_sett($branch_id);
            if (!empty($settings)) {
                if ($flag == 0) {
                    unlink('.uploads/layouts/workstation/' . $settings->background_image);
                    $data = array('background_image' => null);
                } elseif ($flag == 1) {
                    unlink('.uploads/layouts/workstation/' . $settings->logo);
                    $data = array('logo' => null);
                }
                $this->M_general->update('workstation_layout_sett', $settings->id, $data);
                record_log('Update workstation layout settings');
            }
            update_branch_sett('workstation_layout_sett', $branch_id);
            redirect(base_url("C_templates/workstation_layout_settings/$branch_id/success"));
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function unlink_waiting_area_images($branch_id, $flag)
    {
        if (get_p("waiting_areas_lvl", "u")) {
            $settings = $this->m_templates->get_waiting_area_layout_sett($branch_id);
            if (!empty($settings)) {
                if ($flag == 0) {
                    unlink('.uploads/layouts/waiting_area/' . $settings->background_image);
                    $data = array('background_image' => null);
                } elseif ($flag == 1) {
                    unlink('.uploads/layouts/waiting_area/' . $settings->logo);
                    $data = array('logo' => null);
                }
                $this->M_general->update('waiting_area_layout_sett', $settings->id, $data);
                record_log('Update waiting area layout settings');
            }
            update_branch_sett('waiting_area_layout_sett', $branch_id);
            redirect(base_url("C_templates/waiting_area_layout_settings/$branch_id/success"));
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function unlink_QVRequest_layout_images($branch_id, $id, $flag)
    {
        if (get_p("tic_display_sett_lvl", "u")) {
            $settings = $this->m_templates->get_vr_display_sett($id);
            if (!empty($settings)) {
                if ($flag == 0) {
                    unlink('.uploads/templates/' . $settings->background_image);
                    $data = array('background_image' => null);
                } elseif ($flag == 1) {
                    unlink('.uploads/templates/' . $settings->eb_background);
                    $data = ['eb_background' => null];
                } elseif ($flag == 2) {
                    unlink('.uploads/templates/' . $settings->ab_background);
                    $data = ['ab_background' => null];
                } elseif ($flag == 3) {
                    unlink('.uploads/templates/' . $settings->sb_image);
                    $data = array('sb_image' => null);
                } elseif ($flag == 4) {
                    unlink('.uploads/templates/' . $settings->agb_image);
                    $data = array('agb_image' => null);
                } elseif ($flag == 5) {
                    unlink('.uploads/templates/' . $settings->next_back_b_img);
                    $data = array('next_back_b_img' => null);
                } elseif ($flag == 6) {
                    unlink('.uploads/templates/' . $settings->next_back_n_img);
                    $data = array('next_back_n_img' => null);
                } elseif ($flag == 7) {
                    unlink('.uploads/templates/' . $settings->logo);
                    $data = array('logo' => null);
                }
                $this->M_general->update('vr_display_sett', $settings->id, $data);
                record_log('Update Q-VRequest Layout settings');
            }
            update_branch_sett('vr_display_sett', $branch_id);
            redirect(base_url("C_templates/template_vr_displays/$branch_id/success"));
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function arrange_terminals($branch_id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("terminal_lvl", "u")) {
                $this->m_templates->arrange_terminals($this->input->post('terminals_arrange'));
                record_log('Arrange Terminals');
                update_branch_sett('terminals', $branch_id);
                redirect(base_url() . 'C_terminal/loadShow/' . $branch_id . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function qvrequest_display()
    {
        if (get_p("qvrequest_display_lvl", "v") && (check_is_standalone() || !check_is_server())) {
            $arr = array('active' => 'qvrequest_display');
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
            }
            $arr['branch_id'] = $branch_id;
            $arr['displays'] = $this->m_templates->get_vr_displays($branch_id);
            CI_load_view::load_view('qvrequest/select_display', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function qvrequest_display_single($id = null, $branch = null)
    {
        $this->session->unset_userdata('cus_phone');
        if ((!$this->require_login) || get_p("qvrequest_display_lvl", "v")) {
            if (!$this->require_login && $this->session->userdata('qr_access')) {
                $time = (int) $this->session->userdata('qr_access');
                $now = time();
                if (date('Y-m-d', $now) != date('Y-m-d', $time)) {
                    $error['text'] = $this->lang->line('permission_denied');
                    $this->load->view('private/page-500', $error);
                    return;
                }
            }
            $arr['data'] = $this->m_templates->get_vr_display_sett($id);
            if (!$arr['data']) {
                $error['text'] = $this->lang->line('set_qvrequest_page_sett');
                $this->load->view('private/page-500', $error);
                return;
            }
            if (!$this->require_login && empty($branch)) {
                $branch = $arr['data']->template_id;
            }
            if (!$this->require_login && !empty($branch)) {
                $this->session->set_userdata('branch_id', $branch);
            }
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
            }
            $ticket_sett = $this->db->get_where('vr_ticket_sett', array('template_id' => $branch_id))->row();
            if (!$ticket_sett) {
                redirect(base_url('C_templates/template_ticket_settings/' . $branch_id));
            }
            $arr['active'] = 'qvrequest_display';
            $arr['branch_id'] = $branch_id;
            $arr['display_id'] = $id;
            if (DISABLE_QV_LANG) {
                redirect(base_url('C_templates/set_customer_info/' . strtolower(QV_DEFAULT_LANG) . '/' . $id));
            }
            $this->load->view('qvrequest/lang_selection', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function qvrequest_display_single_qr($id = null)
    {
        if (get_p("qvrequest_display_lvl", "v")) {
            $arr['data'] = $this->m_templates->get_vr_display_sett($id);
            if (!$arr['data']) {
                $error['text'] = $this->lang->line('set_qvrequest_page_sett');
                $this->load->view('private/page-500', $error);
                return;
            }
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
            }
            $ticket_sett = $this->db->get_where('vr_ticket_sett', array('template_id' => $branch_id))->row();
            if (!$ticket_sett) {
                redirect(base_url('C_templates/template_ticket_settings/' . $branch_id));
            }
            $hash = ['id' => $id, 'time' => time()];
            $url = base_url('C_templates/qv_qr?hash=' . base64_encode(json_encode($hash)));
            $this->generate_qrcode($url);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }

    }

    public function qv_qr()
    {
        $this->session->unset_userdata('cus_phone');
        $this->session->unset_userdata('qr_access');
        if ($this->input->get('hash')) {
            $decode = json_decode(base64_decode($this->input->get('hash')), true);

            $id = (int) $decode['id'] ?? 0;
            $time = (int) $decode['time'] ?? 0;
            $now = time();
            if (date('Y-m-d', $now) == date('Y-m-d', $time)) {
                $this->session->set_userdata('qr_access', $time);
                redirect(base_url('C_templates/qvrequest_display_single/' . $id));
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }

    }

    public function generate_qrcode($data)
    {
        /* Data */
        $hex_data = bin2hex($data);
        $save_name = $hex_data . '.png';

        /* QR Code File Directory Initialize */
        $dir = 'assets/media/qrcode/';
        if (!file_exists($dir)) {
            mkdir($dir, 0775, true);
        }

        /* QR Configuration  */
        $config['cacheable'] = true;
        $config['imagedir'] = $dir;
        $config['quality'] = true;
        $config['size'] = '2048';
        $config['black'] = array(255, 255, 255);
        $config['white'] = array(255, 255, 255);
        $this->ci_qr_code->initialize($config);

        /* QR Data  */
        $params['data'] = $data;
        $params['level'] = 'L';
        $params['size'] = 30;
        $params['savename'] = FCPATH . $config['imagedir'] . $save_name;

        $this->ci_qr_code->generate($params);

        /* Return Data */
        $return = array(
            'content' => $data,
            'file' => $dir . $save_name,
        );
        return redirect(base_url($return['file']));
    }

    public function set_lang($lang)
    {
        if ($lang == 'ar') {
            $selected_lang = 'arabic';
        } elseif ($lang == 'en') {
            $selected_lang = 'english';
        }
        $this->load->library('user_agent');
        $this->session->unset_userdata('lang');
        $this->session->set_userdata('ticket_lang', $selected_lang);
        redirect($this->agent->referrer());
    }

    public function qvrequest_services($lang = null, $id = null)
    {
        $selected_lang = $lang == 'en' ? 'english' : 'arabic';
        $this->session->set_userdata('ticket_lang', $selected_lang);
        $settings = $this->db->get('OrganisationGlobalSettings')->row();
        if ($id != 3 && $settings->enable_ticket_phone && !$this->input->post('cus_phone', true) && !$this->input->post('cus_reserv', true)) {
            redirect(base_url("C_templates/set_customer_info/$lang/$id"));
        }
        if (!$this->require_login || get_p("qvrequest_display_lvl", "v")) {
            if ($this->input->post('cus_phone', true) || $this->input->post('cus_reserv', true)) {
                $this->session->set_userdata('cus_phone', $this->input->post('cus_phone', true));
                $this->session->set_userdata('cus_reserv', $this->input->post('cus_reserv', true));
                $cus_phone = $this->session->userdata('cus_phone');
                $cus_reserv = $this->session->userdata('cus_reserv');
            }
            $arr['data'] = $this->m_templates->get_vr_display_sett($id);
            if (!$arr['data']) {
                $error['text'] = $this->lang->line('set_qvrequest_page_sett');
                $this->load->view('private/page-500', $error);
                return;
            }
            if (!$this->session->userdata('branch_id')) {
                $this->session->set_userdata('branch_id', $arr['data']->template_id);
            }
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
                return;
            }

            if ($this->input->post('cus_reserv', true)) {
                $reservation_data = $this->db->where('confirmation_no', $this->session->userdata('cus_reserv'))->where('ticket_no', null)->get('reservations')->row();
                if ($reservation_data) {
                    $this->db->select('w_s.service_id,w_s.workflow_id');
                    $this->db->join('workflow_steps w_s', 'w_s.workflow_id=w.id');
                    $workflow_service = $this->db->get_where('workflows w', array('w.branch_id' => $branch_id, 'w_s.service_id' => $reservation_data->service_id))->row();
                    if ($workflow_service) {
                        $workflow_id = $workflow_service->workflow_id;
                        redirect(base_url("C_templates/generateTicket/$workflow_id/$id/$lang"));
                    }
                }
            } else if ($this->input->post('cus_phone', true)) {
				$reservation_data = $this->db->where('mobile_num', $this->session->userdata('cus_phone'))->where('ticket_no', null)->where('datetime >=', date('Y-m-d 00:i:s'))->get('reservations')->result();
                if ($reservation_data) {
					$count =  count($reservation_data);
					if($count == 1) {
						$this->db->select('w_s.service_id,w_s.workflow_id');
						$this->db->join('workflow_steps w_s', 'w_s.workflow_id=w.id');
						$workflow_service = $this->db->get_where('workflows w', array('w.branch_id' => $branch_id, 'w_s.service_id' => $reservation_data[0]->service_id))->row();
						if ($workflow_service) {
							$workflow_id = $workflow_service->workflow_id;
							redirect(base_url("C_templates/generateTicket/$workflow_id/$id/$lang"));
						}
                    } else if($count > 1) {
						$customer_info = $this->db->where('phone', $reservation_data[0]->mobile_num)->get('customers')->row();
						foreach($reservation_data as $k => $v) {
							$this->db->select('w_s.service_id,w_s.workflow_id,s.service_name as englisg_service_name,s.arabic_service_name as arabic_service_name');
							$this->db->join('workflow_steps w_s', 'w_s.workflow_id=w.id');
							$this->db->join('service s', 's.id=w_s.service_id');
							$workflow_service = $this->db->get_where('workflows w', array('w.branch_id' => $branch_id, 'w_s.service_id' => $v->service_id))->row();
							if ($workflow_service) {
								$reservation_data[$k]->workflow_id = $workflow_service->workflow_id;
								$reservation_data[$k]->arabic_service_name = $workflow_service->arabic_service_name;
								$reservation_data[$k]->englisg_service_name = $workflow_service->englisg_service_name;
							}
						}
						$arr['customer_info'] = $customer_info;
						$arr['reservation_data'] = $reservation_data;
						$arr['display_id'] = $id;
						$arr['selected_lang'] = $lang;
						$this->load->view('qvrequest/reservations', $arr);
						return true;
					}
                }
			}
            $branch_workFlow = $this->m_templates->get_branch_workFlow_cat($branch_id, $id);
            $this->db->select('single_service_workflow');
            $settings = $this->db->get('OrganisationGlobalSettings')->row();
            $settings = $settings ? $settings->single_service_workflow : 0;
            $workFlow_parent_cat = array();
            $i = 0;
            foreach ($branch_workFlow as $key => $one) {
                if ($one->cat_id !== null && $one->cat_id != 0) {
                    $parent = $this->m_templates->get_work_flow_parent_cat($one->cat_id);
                } elseif ($one->cat_id == 0 || ($one->cat_id === null && $settings)) {
                    $parent = $one;
                } else {
                    continue;
                }
                if ($this->unique_record($workFlow_parent_cat, $parent)) {
                    $workFlow_parent_cat[] = $parent;
                }
                $i++;
            }
            $arr['work_flow_cat'] = $workFlow_parent_cat;
            $arr['active'] = 'qvrequest_display';
            $arr['branch_id'] = $branch_id;
            $arr['selected_lang'] = $this->session->userdata('ticket_lang');
            $arr['display_id'] = $id;
            $arr['cus_phone'] = $cus_phone;
            $arr['cus_reserv'] = $cus_reserv;
            $arr['selected_lang'] = $lang;
            $this->load->view('qvrequest/services_step', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function unique_record($arr, $entry)
    {
        foreach ($arr as $value) {
            if ((isset($entry->workflow) && isset($value->workflow) && $value->id == $entry->id) ||
                (!isset($entry->workflow) && !isset($value->workflow) && $value->id == $entry->id)) {
                return false;
            }
        }
        return true;
    }

    public function getAllChildren($cat_id)
    {
        $Child_counter = 0;
        $Cat_children[] = $this->m_templates->get_Each_child($cat_id);

        while ((count($Cat_children) > 0) && ($Child_counter < count($Cat_children))) {
            foreach ($Cat_children[$Child_counter] as $one) {
                $Cat_children[] = $this->m_templates->get_Each_child($one->id);
            }
            $Child_counter++;
        }
        /*       ---    for escape empty array   ----     */
        foreach ($Cat_children as $index => $array) {
            if ($array != array()) {
                foreach ($array as $key => $val) {
                    $Cat_children2[] = $val;
                }
            }
        }

        //                    echo '<pre> Cat1 : ';print_r($Cat_children); echo '</pre>';
        //                    echo '<pre> Cat2 : ';print_r($Cat_children2); echo '</pre>';
        //                    die();

        if (!empty($Cat_children[0])) {
            return $Cat_children2;
        } else {
            return array();
        }
    }

    public function get_cat_ofBranch($cat_id, $display_id)
    {
        if ($this->session->userdata('branch_id') != 0) {
            $branch_id = $this->session->userdata('branch_id');
        } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
            $branch_id = $this->session->userdata('updated_new_branch');
        }
        $branch_workFlow = $this->m_templates->get_branch_workFlow_cat_array($branch_id, $display_id);
        $Cat_children = $this->getAllChildren($cat_id);
        $target = array();
        //       echo "<pre> Branch"; print_r($branch_workFlow);echo "<pre>".'<br>';
        //       echo "<pre> Children"; print_r($Cat_children);echo "<pre>".'<br>';

        if (!empty($Cat_children)) {
            /*         $target=array();
            }  else { */
            foreach ($branch_workFlow as $index => $value) {
                foreach ($Cat_children as $index2 => $value2) {
                    if (($value->cat_id) == ($value2->id)) {
                        if (in_array($value2, $target) == false) {
                            $target[] = $value2;
                        }
                    }
                }
            }
        }

        echo json_encode($target);
    }

    public function get_workFlow($category_id, $display_id)
    {
        $branch_id = $this->session->userdata('branch_id');
        $category_workflow = $this->m_templates->get_workFlow_onCategory($category_id, $branch_id, $display_id);
        echo json_encode($category_workflow);
    }

    public function get_workflow_steps($workFlow_id)
    {
        $workflow_steps = $this->m_templates->get_workflow_steps($workFlow_id);
        echo json_encode($workflow_steps);
    }

    public function get_Branch_terminals()
    {
        $terminals = $this->M_terminal->get_branch_terminals_array($this->session->userdata('branch_id'));
        echo json_encode($terminals);
    }

    public function get_service_agent($service_id)
    {
        $branch_id = $this->session->userdata('branch_id');
        $agents = $this->m_templates->get_service_agent($service_id, $branch_id);
        echo json_encode($agents);
    }

    public function generateTicket($workflow_id = null, $display_id = null, $lang = null)
    {

        if ($workflow_id == 'null') {
            $workflow_id = null;
        }
        $branch_id = $this->session->userdata('branch_id');
        if ($workflow_id != null) {
            $required_workflow_id = $workflow_id;
            $this->db->select('w_s.service_id');
            $this->db->join('workflow_steps w_s', 'w_s.workflow_id=w.id');
            $workflow_service = $this->db->get_where('workflows w', array('w.id' => $workflow_id))->row();

            if (!$workflow_service) {
                record_log("ERROR : can't find the selected workflow while generate ticket");
                echo "ERROR : can't find the selected workflow while generate ticket";
                die;
            }
            $services_ids[0][0] = $workflow_service->service_id;
            $trans_data[0] = array(
                'workflow_id' => $workflow_id,
                'branch_id' => $branch_id,
            );
        } else {
            $i = 0;
            $j = 0;
            $iteration = 0;

            foreach ($this->input->post() as $key => $value) {
                if (strpos($key, "workflow_id") !== false) {
                    $trans_data[] = array(
                        'workflow_id' => $value,
                        'branch_id' => $branch_id,
                    );
                    if ($iteration != 0) {
                        $j++;
                        $i = 0;
                    }
                } elseif (strpos($key, "seviceId") !== false) {
                    $index = substr($key, strpos($key, 'seviceId') + 8);
                    if ($this->input->post('Agent_services' . $index)) {
                        $services_ids[$j][$i] = $value;
                        if ($this->input->post('service_agent' . $index)) {
                            $agent_or_terminal = explode('_', $this->input->post('service_agent' . $index));
                            if ($agent_or_terminal[0] == 'A') {
                                $service_agent[$j][$i] = $agent_or_terminal[1];
                                $service_agent_key[$j][$i] = $this->db->get_where('Operator', array('id' => $agent_or_terminal[1]))->row()->key;
                            } elseif ($agent_or_terminal[0] == 'T') {
                                $service_terminal[$j][$i] = $agent_or_terminal[1];
                                $service_terminal_key[$j][$i] = $this->db->get_where('terminal', array('window_no' => $agent_or_terminal[1], 'branch_id' => $branch_id, 'deleted' => 0))->row()->key;
                            }
                        }
                        $i++;
                        $iteration++;
                    }
                } else {
                    $Agent_sevices[$j][$key] = $value;
                }
            }
            $required_workflow_id = $trans_data[0]['workflow_id'];
        }
        $workflow_ids = array();
        $total_sec = 0;
        for ($j = 0; $j < count($trans_data); $j++) {
            if (in_array($trans_data[$j]['workflow_id'], $workflow_ids)) {
                continue;
            }
            array_push($workflow_ids, $trans_data[$j]['workflow_id']);
            $waiting_customers_num += $this->m_templates->get_waiting_clients_per_workflow($trans_data[$j]['workflow_id']);
            $expected_time = $this->m_templates->calculate_expected_time($trans_data[$j]['workflow_id'], $waiting_customers_num);
            $total_sec += $this->m_templates->convert_to_sec($expected_time);
        }
        $expected_time = date('H:i:s', strtotime($this->m_templates->convert_to_time($total_sec)));
        $trans_data[0]['cus_phone'] = $this->session->userdata('cus_phone');
		$date_time =  date('Y-m-d H:i:s');
		$service_id = null;
		if($this->session->userdata('cus_reserv') || $this->session->userdata('cus_phone')) {
			$this->db->select('w_s.service_id');
			$this->db->join('workflow_steps w_s', 'w_s.workflow_id=w.id');
			$workflow_service = $this->db->get_where('workflows w', array('w.branch_id' => $branch_id, 'w_s.workflow_id' => $workflow_id))->row();
			if ($workflow_service) {
				$service_id = $workflow_service->service_id;
			}
		}
        $reservation_data = $this->session->userdata('cus_reserv') || $this->session->userdata('cus_phone') ? $this->db->group_start()->where('confirmation_no', $this->session->userdata('cus_reserv'))->or_where('mobile_num', $this->session->userdata('cus_phone'))->group_end()->where('datetime >=', date('Y-m-d 00:00:00'))->get_where('reservations', ['ticket_no' => null, 'service_id' => $service_id])->row() : null;
		$turn = 0;
		$max_turn = 0;
		$agent_id = 0;
		$agent_key = '';
		$agent_names = [];
        if ($reservation_data) {
            $trans_data[0]['reservation_id'] = $reservation_data->id;
			$date_time = $reservation_data->datetime;
			$turn = $reservation_data->reservation_turn;
			$agent_id = $reservation_data->agent_id;
			$agent_key = $reservation_data->agent_key;
			if($agent_id) {
				$agent_info = $this->db->select('name')->get_where('Operator', ['id' => $agent_id])->row();
				if($agent_info) {
					$agent_names[] = $agent_info->name;
				}
			}
        } else {
			$srvs = [];
			for ($j = 0; $j < count($trans_data); $j++) {
				for ($i = 0; $i < count($services_ids[$j]); $i++) {
					$srvs[] = $services_ids[$j][$i];
				}
			}
			$max_turn = $this->db->select_max('reservation_turn')->where_in('service_id', $srvs)->where('datetime >=', date('Y-m-d 00:00:00'))->get('reservations')->row();
			$max_turn = $max_turn ? $max_turn->reservation_turn + 5 : 0;
		}
        
						
		for ($i = 0; $i < TICKET_GENERATE_COUNT; $i++):
            $Work_transaction_id = $this->m_templates->save_Workflow_transations($trans_data[0], $display_id, $lang, $turn, $max_turn);
        endfor;
        if (!$Work_transaction_id) {
            $error['text'] = 'Contact admin to enter system global settings';
            $this->load->view('private/page-500', $error);
            return;
        }
        $Agent_sevices = [];
        for ($j = 0; $j < count($trans_data); $j++) {
            for ($i = 0; $i < count($services_ids[$j]); $i++) {
                $chk_srv = $this->db->get_where('transactions', ['work_transaction_id' => $Work_transaction_id[0], 'service_id' => $services_ids[$j][$i]])->row();
                if (empty($chk_srv)) {
                    $service_name = $lang == 'en' ? $this->db->get_where('service', array('id' => $services_ids[$j][$i]))->row()->service_name : $this->db->get_where('service', array('id' => $services_ids[$j][$i]))->row()->arabic_service_name;
					if(ENABLE_TERMINAL_PRINT) {
						$this->db->select('t.window_no');
						$this->db->join('terminal_has_service ts', 'ts.terminal_key=t.key');
						$serviceTerminal = $this->db->get_where('terminal t', array('ts.service_id' => $services_ids[$j][$i]))->result();
					}
                    if ($workflow_id != null) {
                        if (ENABLE_TERMINAL_PRINT && $serviceTerminal) {
                            $terminals = [];
                            foreach ($serviceTerminal as $t):
                                $terminals[] = $t->window_no;
                            endforeach;
                            $service_name .= '<br/>' . ($lang == 'en' ? (in_array($services_ids[$j][$i], [56,57]) ? WINDOW_NAME_EN : CLINIC_NAME_EN) : (in_array($services_ids[$j][$i], [56,57]) ? WINDOW_NAME_AR : CLINIC_NAME_AR)) . ' [' . implode(',', $terminals) . ']';
                        }
                       
                    }
					 $Agent_sevices[] = $service_name;
                    $inserted_data = array(
                        'serving_time' => '00:00:00',
                        'waiting_time' => '00:00:00',
                        'terminal_id' => isset($service_terminal[$j][$i]) ? $service_terminal[$j][$i] : '0',
                        'terminal_key' => isset($service_terminal_key[$j][$i]) ? $service_terminal_key[$j][$i] : '',
                        'agent_id' => $agent_id ? $agent_id : (isset($service_agent[$j][$i]) ? $service_agent[$j][$i] : '0'),
                        'agent_key' => $agent_key ? $agent_key : (isset($service_agent_key[$j][$i]) ? $service_agent_key[$j][$i] : ''),
                        'client_name' => '',
                        'client_id_num' => '',
                        'entering_time' => !empty($reservation_data) ? $reservation_data->datetime : date('Y-m-d H:i:s'),
                        'service_id' => $services_ids[$j][$i],
                        'branch_id' => $branch_id,
                        'date' => $date_time,
                        'ticket_num' => $Work_transaction_id[1],
                        'status' => 0,
                        'language' => $this->session->userdata('ticket_lang'),
                        'cname' => '',
                        'vr_service_name' => $service_name,
                        'work_transaction_id' => $Work_transaction_id[0],
                        'work_transaction_key' => $Work_transaction_id[2],
                        'order' => $i + $j,
                        'waiting_customers_num' => $waiting_customers_num,
                        'millensys_number' => null,
                    );
                    //if ($j == 0 && $i == 0) {
                    $inserted_data['its_turn'] = 1;
                    $inserted_data['fake_its_turn'] = 1;
                    //}
                    $ois[] = $this->Crud_model->insert('transactions', $inserted_data);
                    $agent_name = '';
                    if (isset($service_agent[$j][$i])) {
                        $this->db->select('name');
                        $agent = $this->db->get_where('Operator', array('id' => $service_agent[$j][$i]))->row();
                        $agent_name = $agent->name;
                    } elseif (isset($service_terminal[$j][$i])) {
                        $terminal = $this->db->get_where('terminal', array('window_no' => $service_terminal[$j][$i], 'branch_id' => $branch_id, 'deleted' => 0))->row();
                        if ($terminal) {
                            $this->db->select('Operator.name');
                            $this->db->join('Operator', "Operator.key=sessions.agent_key and sessions.user_type='Agent'");
                            $agent = $this->db->get_where('sessions', array('terminalID' => $terminal->id, 'logout_time' => null))->row();
                            if ($agent) {
                                $agent_name = $agent->name;
                            }
                        }
                    }
                    $agent_names[] = $agent_name;
                }
            }
        }
        $this->session->set_flashdata('message', "Added successfully");
        record_log("ticket generated with number: " . $Work_transaction_id[1]);
        $general_settings = $this->db->get('OrganisationGlobalSettings')->row();
        $ticket_sett = $this->db->get_where('vr_ticket_sett', array('template_id' => $branch_id, 'ticket_lang' => $this->session->userdata('ticket_lang')))->row();
        $this->db->select('EnglishBranchName,ArabicBranchName');
        $branch_data = $this->db->get_where('Branch', array('BranchID' => $branch_id))->row();
        $ticket_header_ad = $this->m_templates->get_header_ad($branch_id, $ticket_sett->ticket_lang);
        $ticket_footer_ad = $this->m_templates->get_footer_ad($branch_id, $ticket_sett->ticket_lang);
        //        print_r($Agent_sevices);
        //        die;
        if ($reservation_data) {
            $this->db->where('id', $reservation_data->id)->update('reservations', ['ticket_no' => $Work_transaction_id[1]]);
        }
        $arr = array('general_settings' => $general_settings, 'ticket_settings' => $ticket_sett,
            'agents' => $agent_names, 'services' => $Agent_sevices, 'branch_data' => $branch_data,
            'ticket_num' => $Work_transaction_id[1], 'waiting_customers_num' => $waiting_customers_num,
            'expected_time' => $expected_time, 'ticket_footer_ad' => $ticket_footer_ad, 'ticket_header_ad' => $ticket_header_ad, 'branch_id' => $branch_id,
            'work_transaction_id' => $Work_transaction_id[0]);
        $ticket_soft_copy = $this->load->view('transactions/ticket', $arr, true);
        //$this->Crud_model->send_customer_notifications($this->session->userdata('cus_phone'), $this->session->userdata('cus_reserv'), 'welcome_msg', $ticket_soft_copy, $this->session->userdata('ticket_lang'));
        $ticket_msg = $this->session->userdata('ticket_lang') == 'arabic' ? 'رقم التذكرة : ' . $Work_transaction_id[1] : 'Your ticket number: ' . $Work_transaction_id[1];
        $this->Crud_model->send_customer_ticket($this->session->userdata('cus_phone'), $ticket_msg, $this->session->userdata('ticket_lang') == 'arabic' ? 'arabic' : null);
        $arr['display_id'] = $display_id;
        $this->db->where('id', $Work_transaction_id[0])->update('work_transactions', ['ticket_copy' => serialize($arr)]);
        $this->load->view('transactions/ticket', $arr);
    }

    public function reprint_ticket($Work_transaction_id = null, $redirect = false, $display_id = 0)
    {
        $work_transaction = $this->db->get_where('work_transactions w', array('w.id' => $Work_transaction_id))->row();
        if ($work_transaction) {
            if (!empty($work_transaction->ticket_copy)) {
                $arr = unserialize($work_transaction->ticket_copy);
                $arr['redirect'] = $redirect;
                $arr['display_id'] = $display_id;
                $this->load->view('transactions/reprint_ticket', $arr);
            } else {
                redirect(base_url("C_templates/qvrequest_display_single/$display_id"));
            }
        }
    }

    public function delete_slideshow_photo($photo_id, $photo_nme, $station_waiting = 0)
    {
        if ($station_waiting == 0) {
            $access_level = get_p("workstations_lvl", "d");
        } else {
            $access_level = get_p("waiting_areas_lvl", "d");
        }
        if ($access_level == '1') {
            $this->db->delete('slideshow_photos', array('id' => $photo_id));
            $text = $station_waiting == 0 ? 'Workstation' : 'Waiting area';
            record_log('Delete slideshow img : ' . $photo_nme . ' for ' . $text);
            if ($station_waiting == 0) {
                unlink('./uploads/layouts/workstation/' . $photo_nme);
            } else {
                $photo_still_linked = $this->db->get_where('slideshow_photos', array('photo' => $photo_nme))->row();
                if (!$photo_still_linked) {
                    unlink('./uploads/layouts/waiting_area/' . $photo_nme);
                }
            }
            echo 'yes';
        } else {
            echo 'no';
        }
    }

    public function delete_waiting_area($branch_id, $id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("waiting_areas_lvl", "d")) {
                $row = $this->db->get_where('waiting_areas', ['id' => $id, 'branch_id' => $branch_id])->row();
                if ($row) {
                    $this->db->delete('waiting_areas', ['id' => $id]);
                    $this->db->delete('waiting_area_layout_sett', ['waiting_area_id' => $id]);
                    $this->db->delete('waiting_area_terminals', ['waiting_area_id' => $id]);
                    record_log('Delete waiting area : ' . $row->name);
                }
                redirect(site_url() . 'C_templates/waiting_area_layout_settings/' . $branch_id . '/NULL/' . ($row ? 'success' : 'fail'));
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function no_permission_error_page()
    {
        $error['text'] = $this->lang->line('permission_denied');
        $this->load->view('private/page-500', $error);
    }

    public function delete_vr_request_display($id)
    {
        if ($this->session->userdata('login')) {
            if (get_p("tic_display_sett_lvl", "d")) {
                $row = $this->db->get_where('vr_display_sett', array('id' => $id))->row();
                if ($row) {
                    $this->db->delete('qvrequest_workflow', array('display_id' => $id));
                    $this->m_templates->delete_qv_request_display($id);
                    record_log('Delete QV request display : ' . $row->title);
                }
                redirect(site_url() . 'C_templates/template_vr_displays/' . $row->template_id . '/success');
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function reprintTicket($lang, $id)
    {
        $selected_lang = $lang == 'en' ? 'english' : 'arabic';
        $this->session->set_userdata('ticket_lang', $selected_lang);
        if (!$this->require_login || get_p("qvrequest_display_lvl", "v")) {
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr['data'] = $this->m_templates->get_vr_display_sett($id);
            if (!$arr['data']) {
                $error['text'] = $this->lang->line('set_qvrequest_page_sett');
                $this->load->view('private/page-500', $error);
            }
            $arr['active'] = 'qvrequest_display';
            $arr['branch_id'] = $branch_id;
            $arr['selected_lang'] = $this->session->userdata('ticket_lang');
            $arr['display_id'] = $id;
            $this->load->view('qvrequest/reprint_ticket', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_ticket_to_reprint($lang, $id)
    {
        ///if (get_p("reprint_ticket_lvl", "v")) {
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
				$branch_id = 1;
			}
            $ticket_number = $this->input->post('ticket_number');
            $this->load->model('M_workflow');
            $client_workflow = $this->M_workflow->get_client_workflow_reprint($ticket_number, $branch_id);
            if ($client_workflow) {
                redirect(base_url("C_templates/reprint_ticket/" . $client_workflow[0]->work_transaction_id . "/true/$id"));
            } else {
                redirect(base_url("C_templates/qvrequest_display_single/$id"));
            }
        //}
    }

    public function reprintTicketData($lang, $id)
    {
        $selected_lang = $lang == 'en' ? 'english' : 'arabic';
        $this->session->set_userdata('ticket_lang', $selected_lang);
        if (!$this->require_login || get_p("qvrequest_display_lvl", "v")) {
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr['data'] = $this->m_templates->get_vr_display_sett($id);
            if (!$arr['data']) {
                $error['text'] = $this->lang->line('set_qvrequest_page_sett');
                $this->load->view('private/page-500', $error);
            }
            $arr['active'] = 'qvrequest_display';
            $arr['branch_id'] = $branch_id;
            $arr['selected_lang'] = $this->session->userdata('ticket_lang');
            $arr['display_id'] = $id;
            $this->load->view('qvrequest/reprint_ticket', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function set_customer_info($lang, $id)
    {
        $settings = $this->db->get('OrganisationGlobalSettings')->row();
        if (!$settings->enable_ticket_phone || $id == 3) {
            redirect(base_url("C_templates/qvrequest_services/$lang/$id"));
        }
        $selected_lang = $lang == 'en' ? 'english' : 'arabic';
        $this->session->set_userdata('ticket_lang', $selected_lang);
        if (!$this->require_login || get_p("qvrequest_display_lvl", "v")) {
            if ($this->session->userdata('branch_id') != 0) {
                $branch_id = $this->session->userdata('branch_id');
            } elseif ($this->session->userdata('branch_id') == 0 && $this->session->userdata('updated_new_branch') != 0) {
                $branch_id = $this->session->userdata('updated_new_branch');
            } else {
                $error['text'] = $this->lang->line('error_branch_select');
                $this->load->view('private/page-500', $error);
                return;
            }
            $arr['data'] = $this->m_templates->get_vr_display_sett($id);
            if (!$arr['data']) {
                $error['text'] = $this->lang->line('set_qvrequest_page_sett');
                $this->load->view('private/page-500', $error);
            }
            $branch_workFlow = $this->m_templates->get_branch_workFlow_cat($branch_id, $id);
            $this->db->select('single_service_workflow');
            $settings = $this->db->get('OrganisationGlobalSettings')->row();
            $settings = $settings ? $settings->single_service_workflow : 0;
            $workFlow_parent_cat = array();
            $i = 0;
            foreach ($branch_workFlow as $key => $one) {
                if ($one->cat_id !== null && $one->cat_id != 0) {
                    $parent = $this->m_templates->get_work_flow_parent_cat($one->cat_id);
                } elseif ($one->cat_id == 0 || ($one->cat_id === null && $settings)) {
                    $parent = $one;
                } else {
                    continue;
                }
                if ($this->unique_record($workFlow_parent_cat, $parent)) {
                    $workFlow_parent_cat[] = $parent;
                }
                $i++;
            }
            $arr['work_flow_cat'] = $this->m_templates->get_work_flow_cat($branch_id);
            $arr['work_flow_cat'] = $workFlow_parent_cat;
            $arr['active'] = 'qvrequest_display';
            $arr['branch_id'] = $branch_id;
            $arr['selected_lang'] = $this->session->userdata('ticket_lang');
            $arr['display_id'] = $id;
            $this->load->view('qvrequest/customer_info', $arr);
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function check_confirmation_no($confirmation_no)
    {
        $result = $this->db->get_where('reservations', array('confirmation_no' => $confirmation_no, 'ticket_no' => null))->row();
        if ($result) {
            echo json_encode($result);
        } else {
            echo json_encode(false);
        }
    } 
	
	public function check_mobile_num_no($mobile_num)
    {
        $result = $this->db->get_where('reservations', array('mobile_num' => $mobile_num, 'ticket_no' => null))->row();
        if ($result) {
            echo json_encode($result);
        } else {
            echo json_encode(false);
        }
    }

    public function waiting_areas($branch_id, $status = null)
    {
        if ($this->session->userdata('login') && isset($this->session->userdata("license")['number_of_supported_waiting_areas']) && $this->session->userdata("license")['number_of_supported_waiting_areas']['open'] == 1 && get_p("waiting_areas_lvl", "v")) {
            if (!empty($branch_id)) {
                $this->db->select('*');
                $arr['displays'] = $this->db->get_where('waiting_areas', ['branch_id' => $branch_id])->result();
                if ($status != null) {
                    $arr['status'] = $status;
                }
                $arr['branch_id'] = $branch_id;
                $arr['active'] = 'waiting_area_layout';
                CI_load_view::load_view('branch/waiting_areas', $arr);
            } else {
                $error['text'] = $this->lang->line('branch_not_found');
                $this->load->view('private/page-500', $error);
                return;
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function add_waiting_area($branch_id, $status = null)
    {
        if ($this->session->userdata('login') && isset($this->session->userdata("license")['number_of_supported_waiting_areas']) && $this->session->userdata("license")['number_of_supported_waiting_areas']['open'] == 1 && get_p("waiting_areas_lvl", "c")) {
            if (!empty($branch_id)) {
                $arr['active'] = 'waiting_area_layout';
                $arr['branch_id'] = $branch_id;
                $arr['branch_name'] = $this->m_templates->get_branch_name($branch_id);
                if (!$arr['branch_name']) {
                    $error['text'] = $this->lang->line('branch_not_found');
                    $this->load->view('private/page-500', $error);
                    return;
                }
                if ($status != null) {
                    $arr['status'] = $status;
                }
                CI_load_view::load_view('branch/add_waiting_area', $arr);
            } else {
                $error['text'] = $this->lang->line('branch_not_found');
                $this->load->view('private/page-500', $error);
                return;
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function save_waiting_area($branch_id, $status = null)
    {
        if ($this->session->userdata('login') && get_p("waiting_areas_lvl", "c")) {
            $this->form_validation->set_rules('name', 'Name', 'trim|required|callback_unique_waiting_area_name[' . $branch_id . ']');
            $this->form_validation->set_rules('number', 'Number', 'trim|required|callback_unique_waiting_area_num[' . $branch_id . ']');
            $this->form_validation->set_rules('lang', 'Language', 'trim|required');
            if (!$this->form_validation->run()) {
                $this->add_waiting_area($branch_id);
            } else {
                if (empty($this->input->post('lang'))) {
                    $this->session->set_flashdata('error', 'Select language');
                    redirect(base_url("C_templates/waiting_areas/$branch_id"));
                }
                $data = [
                    'name' => $this->input->post('name'),
                    'number' => $this->input->post('number'),
                    'lang' => $this->input->post('lang'),
                    'branch_id' => $branch_id,
                ];
                $this->m_templates->insert_waiting_area($data);
                record_log('Add new waiting area');
                $this->session->set_flashdata('msg', $this->lang->line('data_submitted_successfully'));
                redirect(base_url("C_templates/waiting_areas/$branch_id"));
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
            return;
        }
    }

    public function unique_waiting_area_name($name, $branch_id)
    {
        $existance_count = $this->db->get_where('waiting_areas', ['branch_id' => $branch_id, 'name' => $name])->num_rows();
        if ($existance_count > 0) {
            $this->form_validation->set_message('unique_waiting_area_name', 'The name must be unique');
            return false;
        }
        return true;
    }

    public function unique_waiting_area_num($number, $branch_id)
    {
        $existance_count = $this->db->get_where('waiting_areas', ['branch_id' => $branch_id, 'number' => $number])->num_rows();
        if ($existance_count > 0) {
            $this->form_validation->set_message('unique_waiting_area_num', 'The number must be unique');
            return false;
        }
        return true;
    }

    public function edit_waiting_area($id, $status = null)
    {
        if ($this->session->userdata('login') && isset($this->session->userdata("license")['number_of_supported_waiting_areas']) && $this->session->userdata("license")['number_of_supported_waiting_areas']['open'] == 1 && get_p("waiting_areas_lvl", "u")) {
            if (!empty($id)) {
                $info = $this->db->get_where('waiting_areas', ['id' => $id])->row();
                if (!empty($info)) {
                    $arr['active'] = 'waiting_area_layout';
                    $arr['id'] = $id;
                    $arr['info'] = $info;
                    $arr['branch_name'] = $this->m_templates->get_branch_name($info->branch_id);
                    if (!$arr['branch_name']) {
                        $error['text'] = $this->lang->line('branch_not_found');
                        $this->load->view('private/page-500', $error);
                        return;
                    }
                    if ($status != null) {
                        $arr['status'] = $status;
                    }
                    CI_load_view::load_view('branch/edit_waiting_area', $arr);
                } else {
                    $error['text'] = $this->lang->line('no_waiting_area');
                    $this->load->view('private/page-500', $error);
                }
            } else {
                $error['text'] = $this->lang->line('parameters_missing');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function update_waiting_area($id, $status = null)
    {
        if ($this->session->userdata('login') && get_p("waiting_areas_lvl", "u")) {
            $success = true;
            $info = $this->db->get_where('waiting_areas', ['id' => $id])->row();
            if (!empty($info)) {
                if ($info->name != $this->input->post('name')) {
                    $success = false;
                    $this->form_validation->set_rules('name', 'Name', 'trim|required|callback_unique_waiting_area_name[' . $info->branch_id . ']');
                }
                if ($info->number != $this->input->post('number')) {
                    $success = false;
                    $this->form_validation->set_rules('number', 'Number', 'trim|required|callback_unique_waiting_area_num[' . $info->branch_id . ']');
                }
                if (empty($this->input->post('lang'))) {
                    $success = false;
                    $this->form_validation->set_rules('lang', 'Language', 'trim|required');
                }
                if (!$this->form_validation->run() && $info && !$success) {
                    $this->edit_waiting_area($id);
                } else {
                    if (empty($this->input->post('lang'))) {
                        $this->session->set_flashdata('error', 'Select language');
                        redirect(base_url("C_templates/waiting_areas/$info->branch_id"));
                    }
                    $data = [
                        'name' => $this->input->post('name'),
                        'number' => $this->input->post('number'),
                        'lang' => $this->input->post('lang'),
                    ];
                    $this->m_templates->update_waiting_area($id, $data);
                    record_log('Update waiting area');
                    $this->session->set_flashdata('msg', $this->lang->line('data_submitted_successfully'));
                    redirect(base_url("C_templates/waiting_areas/$info->branch_id"));
                }
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
                return;
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
            return;
        }
    }

    public function copy_settings()
    {
        if ($this->session->userdata('login') && get_p("waiting_areas_lvl", "v")) {
            $branch_id = $this->input->post('b_id');
            $w_a_id = $this->input->post('w_a_id');
            $waiting_areas = $this->input->post('waiting_area_id');
            if (empty($branch_id) || empty($w_a_id)) {
                $this->session->set_flashdata('error', 'Missing Parameters');
                redirect(base_url("C_templates/waiting_areas/$branch_id"));
            }
            if (empty($waiting_areas)) {
                $this->session->set_flashdata('error', 'Specify waiting areas');
                redirect(base_url("C_templates/waiting_areas/$branch_id"));
            }
            $w_a_info = $this->m_templates->get_waiting_area_sett_info($branch_id, $w_a_id);
            $w_a_videos = $this->m_templates->workstation_videos(1, $branch_id, $w_a_id);
            $w_a_sliders = $this->m_templates->workstation_sliders(1, $branch_id, $w_a_id);
            $w_a_newsbar_en = $this->m_templates->workstation_newsbar_en(1, $branch_id, $w_a_id);
            $w_a_newsbar_ar = $this->m_templates->workstation_newsbar_ar(1, $branch_id, $w_a_id);
            if (!empty($w_a_info)) {
                foreach ($waiting_areas as $w) {
                    // Delete main settings
                    $w_a_data = $this->m_templates->get_waiting_area_sett_info($branch_id, $w);
                    if ($w_a_data) {
                        $this->db->delete('waiting_area_layout_sett', ['branch_id' => $branch_id, 'waiting_area_id' => $w]);
                    }
                    // Delete videos
                    $w_a_v = $this->m_templates->workstation_videos(1, $branch_id, $w);
                    if ($w_a_v) {
                        $this->db->delete('uploaded_videos', ['type' => 1, 'branch_id' => $branch_id, 'w_a_id' => $w]);
                    }
                    // Delete sliders
                    $w_a_s = $this->m_templates->workstation_sliders(1, $branch_id, $w);
                    if ($w_a_s) {
                        $this->db->delete('slideshow_photos', ['type' => 1, 'branch_id' => $branch_id, 'work_waiting_id' => $w]);
                    }
                    // Delete newsbar en
                    $w_a_new_en = $this->m_templates->workstation_newsbar_en(1, $branch_id, $w);
                    if ($w_a_new_en) {
                        $this->db->delete('newsbar_en', ['type' => 1, 'branch_id' => $branch_id, 'w_a_id' => $w]);
                    }
                    // Delete newsbar ar
                    $w_a_new_ar = $this->m_templates->workstation_newsbar_ar(1, $branch_id, $w);
                    if ($w_a_new_ar) {
                        $this->db->delete('newsbar_ar', ['type' => 1, 'branch_id' => $branch_id, 'w_a_id' => $w]);
                    }
                    // Start to copy data from main waiting area
                    unset($w_a_info->id);
                    unset($w_a_info->waiting_area_id);
                    $w_a_info->waiting_area_id = $w;
                    $this->db->insert('waiting_area_layout_sett', $w_a_info);
                    // Start to copy data from videos
                    if (!empty($w_a_videos)) {
                        foreach ($w_a_videos as $wv) {
                            unset($wv->id);
                            unset($wv->w_a_id);
                            $wv->w_a_id = $w;
                            $this->db->insert('uploaded_videos', $wv);
                        }
                    }
                    // Start to copy data from sliders
                    if (!empty($w_a_sliders)) {
                        foreach ($w_a_sliders as $ws) {
                            unset($ws->id);
                            unset($ws->work_waiting_id);
                            $ws->work_waiting_id = $w;
                            $this->db->insert('slideshow_photos', $ws);
                        }
                    }
                    // Start to copy data from newsbar en
                    if (!empty($w_a_newsbar_en)) {
                        foreach ($w_a_newsbar_en as $n_en) {
                            unset($n_en->id);
                            unset($n_en->w_a_id);
                            $n_en->w_a_id = $w;
                            $this->db->insert('newsbar_en', $n_en);
                        }
                    }
                    // Start to copy data from newsbar ar
                    if (!empty($w_a_newsbar_ar)) {
                        foreach ($w_a_newsbar_ar as $n_ar) {
                            unset($n_ar->id);
                            unset($n_ar->w_a_id);
                            $n_ar->w_a_id = $w;
                            $this->db->insert('newsbar_ar', $n_ar);
                        }
                    }
                }
                $this->session->set_flashdata('msg', $this->lang->line('data_submitted_successfully'));
                redirect(base_url("C_templates/waiting_areas/$branch_id"));
            } else {
                $this->session->set_flashdata('error', $this->lang->line('data_not_found'));
                redirect(base_url("C_templates/waiting_areas/$branch_id"));
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function delete_waiting_area_new($w, $branch_id)
    {
        if ($this->session->userdata('login') && get_p("waiting_areas_lvl", "d")) {
            if (!empty($w) && !empty($branch_id)) {
                // Get main waiting area info
                $w_a_info = $this->db->get_where('waiting_areas', ['id' => $w])->row();
                if (!empty($w_a_info)) {
                    // Delete main settings
                    $w_a_data = $this->m_templates->get_waiting_area_sett_info($branch_id, $w);
                    if ($w_a_data) {
                        $this->db->delete('waiting_area_layout_sett', ['branch_id' => $branch_id, 'waiting_area_id' => $w]);
                    }
                    // Delete videos
                    $w_a_v = $this->m_templates->workstation_videos(1, $branch_id, $w);
                    if ($w_a_v) {
                        $this->db->delete('uploaded_videos', ['type' => 1, 'branch_id' => $branch_id, 'w_a_id' => $w]);
                    }
                    // Delete sliders
                    $w_a_s = $this->m_templates->workstation_sliders(1, $branch_id, $w);
                    if ($w_a_s) {
                        $this->db->delete('slideshow_photos', ['type' => 1, 'branch_id' => $branch_id, 'work_waiting_id' => $w]);
                    }
                    // Delete newsbar en
                    $w_a_new_en = $this->m_templates->workstation_newsbar_en(1, $branch_id, $w);
                    if ($w_a_new_en) {
                        $this->db->delete('newsbar_en', ['type' => 1, 'branch_id' => $branch_id, 'w_a_id' => $w]);
                    }
                    // Delete newsbar ar
                    $w_a_new_ar = $this->m_templates->workstation_newsbar_ar(1, $branch_id, $w);
                    if ($w_a_new_ar) {
                        $this->db->delete('newsbar_ar', ['type' => 1, 'branch_id' => $branch_id, 'w_a_id' => $w]);
                    }
                    // Delete main waiting area
                    $this->db->delete('waiting_areas', ['id' => $w_a_info->id]);
                    $this->session->set_flashdata('msg', $this->lang->line('data_deleted_successfully'));
                    redirect(base_url("C_templates/waiting_areas/$branch_id"));
                } else {
                    $this->session->set_flashdata('error', $this->lang->line('data_not_found'));
                    redirect(base_url("C_templates/waiting_areas/$branch_id"));
                }
            } else {
                $this->session->set_flashdata('error', $this->lang->line('parameters_missing'));
                redirect(base_url("C_templates/waiting_areas/$branch_id"));
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function get_sub_waiting_areas()
    {
        $id = $this->input->post('id');
        $branch_id = $this->input->post('branch_id');
        $waiting_areas = $this->m_templates->get_branch_waiting_areas($branch_id, $id);
        echo json_encode($waiting_areas);
    }

    public function waiting_area_settings($branch_id, $id = null, $status_2 = null)
    {
        if ($this->session->userdata('login')) {
            if (!empty($branch_id) && !empty($id)) {
                if (isset($this->session->userdata("license")['number_of_supported_waiting_areas']) && $this->session->userdata("license")['number_of_supported_waiting_areas']['open'] == 1 && get_p("waiting_areas_lvl", "v")) {
                    $this->load->model('M_general');
                    $user_brnches = $this->M_general->get_user_branches();
                    if (!in_array($branch_id, $user_brnches)) {
                        $error['text'] = $this->lang->line('permission_to_branch');
                        $this->load->view('private/page-500', $error);
                        return;
                    }
                    if ($status_2 != null) {
                        $arr['status'] = $status_2;
                    }
                    $arr['services'] = $this->m_templates->get_branch_services($branch_id);
                    $arr['waiting_area_videos'] = $this->m_templates->workstation_videos(1, $branch_id, $id);
                    $arr['w_a_services'] = $this->m_templates->waiting_area_services($id);
                    $arr['waiting_area_sliders'] = $this->m_templates->workstation_sliders(1, $branch_id, $id);
                    $arr['waiting_area_layout_sett'] = $this->m_templates->get_waiting_area_sett_info($branch_id, $id);
                    $arr['active'] = 'waiting_area_layout';
                    $arr['id'] = $id;
                    $arr['branch_id'] = $branch_id;
                    $arr['info'] = $this->db->get_where('waiting_areas', ['id' => $id])->row();
                    if (!$arr['info']) {
                        $error['text'] = $this->lang->line('no_waiting_area');
                        $this->load->view('private/page-500', $error);
                        return;
                    }
                    $arr['branch_name'] = $this->m_templates->get_branch_name($branch_id);
                    if (!$arr['branch_name']) {
                        $error['text'] = $this->lang->line('branch_not_found');
                        $this->load->view('private/page-500', $error);
                        return;
                    }
                    CI_load_view::load_view('branch/waiting_area_layout_1', $arr);
                } else {
                    $error['text'] = $this->lang->line('permission_denied');
                    $this->load->view('private/page-500', $error);
                }
            } else {
                $this->session->set_flashdata('error', $this->lang->line('parameters_missing'));
                redirect(base_url("C_templates/waiting_areas/$branch_id"));
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function download_newbar_en_file($branch_id = null, $id = null)
    {
        //load our new PHPExcel library
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $this->db->order_by('id');
        if (!empty($branch_id)) {
            $this->db->where('branch_id', $branch_id);
        }
        if (!empty($id)) {
            $this->db->where('w_a_id', $id);
        }
        $this->db->where('type', 1);
        $newsbar = $this->db->get('newsbar_en')->result();
        $i = 1;
        foreach ($newsbar as $news) {
            $sheet->setCellValue("A$i", $news->description);
            $i++;
        }
        $filename = 'newsbar_en.xlsx'; //save our workbook as this file name
        // Redirect output to a client’s web browser (Excel2007)
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        // If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');
        // If you're serving to IE over SSL, then the following may be needed
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0
        //save it to Excel5 format (excel 2003 .XLS file), change this to 'Excel2007' (and adjust the filename extension, also the header mime type)
        //if you want to save it as .XLSX Excel 2007 format
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
    }

    public function download_newbar_en_file_ar($branch_id = null, $id = null)
    {
        //load our new PHPExcel library
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $this->db->order_by('id');
        if (!empty($branch_id)) {
            $this->db->where('branch_id', $branch_id);
        }
        if (!empty($id)) {
            $this->db->where('w_a_id', $id);
        }
        $this->db->where('type', 1);
        $newsbar = $this->db->get('newsbar_ar')->result();
        $i = 1;
        foreach ($newsbar as $news) {
            $sheet->setCellValue("A$i", $news->description);
            $i++;
        }
        $filename = 'newsbar_ar.xlsx'; //save our workbook as this file name
        // Redirect output to a client’s web browser (Excel2007)
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        // If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');
        // If you're serving to IE over SSL, then the following may be needed
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0
        //save it to Excel5 format (excel 2003 .XLS file), change this to 'Excel2007' (and adjust the filename extension, also the header mime type)
        //if you want to save it as .XLSX Excel 2007 format
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
    }

    public function save_waiting_area_settings($branch_id, $id)
    {
        if (!empty($branch_id) && !empty($id)) {
            if (get_p("waiting_areas_lvl", "u")) {
                if (!is_numeric($this->input->post("page_size")) || $this->input->post("page_size") < 1) {
                    $this->session->set_flashdata('error', 'Page rows for currencies is not valid');
                    redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                }
                if (!is_numeric($this->input->post("service_page")) || $this->input->post("service_page") < 1) {
                    $this->session->set_flashdata('error', 'Services/Page for service bar is not valid');
                    redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                }
                if (!is_numeric($this->input->post("srv_tbl_page")) || $this->input->post("srv_tbl_page") < 1) {
                    $this->session->set_flashdata('error', 'Services/Page for service table is not valid');
                    redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                }
                $data = array(
                    'time' => time(),
                    'branch_id' => $branch_id,
                    'show_logo' => $this->input->post('show_logo') ? 1 : 0,
                    'logo_top_margin' => $this->input->post('logo_top_margin'),
                    'logo_left_margin' => $this->input->post('logo_left_margin'),
                    'logo_width' => $this->input->post('logo_width'),
                    'logo_height' => $this->input->post('logo_height'),
                    'show_date' => $this->input->post('show_date') ? 1 : 0,
                    'date_top_margin' => $this->input->post('date_top_margin'),
                    'date_left_margin' => $this->input->post('date_left_margin'),
                    'date_font_size' => $this->input->post('date_font_size'),
                    'show_video' => $this->input->post('show_video') ? 1 : 0,
                    'video_margin_top' => $this->input->post('video_margin_top'),
                    'video_percentage' => $this->input->post('video_percentage'),
                    'show_slider' => $this->input->post('show_slider') ? 1 : 0,
                    'slider_percentage' => $this->input->post('slider_percentage'),
                    'rotation' => $this->input->post('rotation'),
                    'background_color' => $this->input->post('back_color'),
                    'terminal_font' => $this->input->post('terminal_font'),
                    'terminal_font_size' => $this->input->post('terminal_font_size'),
                    'terminal_font_color' => $this->input->post('terminal_font_color'),
                    'ticket_num_font' => $this->input->post('ticket_num_font'),
                    'ticket_num_font_size' => $this->input->post('ticket_num_font_size'),
                    'ticket_num_font_color' => $this->input->post('ticket_num_font_color'),
                    'header_font' => $this->input->post('header_font'),
                    'header_font_size' => $this->input->post('header_font_size'),
                    'header_font_color' => $this->input->post('header_font_color'),
                    'header_alignment' => $this->input->post('header_alignment'),
                    'terminal_alignment' => $this->input->post('terminal_alignment'),
                    'ticket_alignment' => $this->input->post('ticket_alignment'),
                    'news_font' => $this->input->post("news_font"),
                    'news_size' => $this->input->post("news_size"),
                    'news_color' => $this->input->post("news_color"),
                    'news_spacing' => $this->input->post("news_spacing"),
                    'news_speed' => $this->input->post("news_speed"),
                    'news_lang' => $this->input->post("news_lang"),
                    'news_dir' => $this->input->post("news_dir"),
                    'news_bg_color' => $this->input->post('news_bg_color'),
                    'show_en_newsbar' => $this->input->post('show_en_newsbar') ? 1 : 0,
                    'show_ar_newsbar' => $this->input->post('show_ar_newsbar') ? 1 : 0,
                    'separator_width_en' => $this->input->post('separator_width_en'),
                    'separator_height_en' => $this->input->post('separator_height_en'),
                    'news_font_ar' => $this->input->post("news_font_ar"),
                    'news_size_ar' => $this->input->post("news_size_ar"),
                    'news_color_ar' => $this->input->post("news_color_ar"),
                    'news_spacing_ar' => $this->input->post("news_spacing_ar"),
                    'news_speed_ar' => $this->input->post("news_speed_ar"),
                    'news_dir_ar' => $this->input->post("news_dir_ar"),
                    'news_bg_color_ar' => $this->input->post('news_bg_color_ar'),
                    'separator_width_ar' => $this->input->post('separator_width_ar'),
                    'separator_height_ar' => $this->input->post('separator_height_ar'),
                    'show_currency' => $this->input->post('show_currency') ? 1 : 0,
                    'currency_size' => $this->input->post("currency_size"),
                    'currencies_paging' => $this->input->post('currencies_paging') ? 1 : 0,
                    'page_size' => $this->input->post("page_size"),
                    'curr_top_margin' => $this->input->post('curr_top_margin'),
                    'curr_left_margin' => $this->input->post('curr_left_margin'),
                    'curr_header_bg' => $this->input->post('curr_header_bg'),
                    'curr_header_font' => $this->input->post('curr_header_font'),
                    'curr_header_font_color' => $this->input->post('curr_header_font_color'),
                    'curr_header_font_size' => $this->input->post('curr_header_font_size'),
                    'curr_data_font_color' => $this->input->post('curr_data_font_color'),
                    'curr_data_font' => $this->input->post('curr_data_font'),
                    'curr_data_bg' => $this->input->post('curr_data_bg'),
                    'curr_data_font_size' => $this->input->post('curr_data_font_size'),
                    'show_service_bar' => $this->input->post('show_service_bar') ? 1 : 0,
                    'enable_service_page' => $this->input->post('enable_service_page') ? 1 : 0,
                    'service_page' => $this->input->post('service_page'),
                    'service_paging_type' => $this->input->post('service_paging_type'),
                    'srv_h_bg' => $this->input->post('srv_h_bg'),
                    'srv_h_color' => $this->input->post('srv_h_color'),
                    'srv_h_font_size' => $this->input->post('srv_h_font_size'),
                    'srv_h_font_family' => $this->input->post('srv_h_font_family'),
                    'srv_d_bg' => $this->input->post('srv_d_bg'),
                    'srv_d_color' => $this->input->post('srv_d_color'),
                    'srv_d_font_size' => $this->input->post('srv_d_font_size'),
                    'srv_d_font_family' => $this->input->post('srv_d_font_family'),
                    'counter_ar_head' => $this->input->post('counter_ar_head'),
                    'counter_en_head' => $this->input->post('counter_en_head'),
                    'customer_ar_head' => $this->input->post('customer_ar_head'),
                    'customer_en_head' => $this->input->post('customer_en_head'),
                    'show_service_tbl' => $this->input->post('show_service_tbl') ? 1 : 0,
                    'srv_tbl_terminals' => $this->input->post('srv_tbl_terminals'),
                    'srv_tbl_top_margin' => $this->input->post('srv_tbl_top_margin'),
                    'srv_tbl_left_margin' => $this->input->post('srv_tbl_left_margin'),
                    'srv_tbl_percentage' => $this->input->post('srv_tbl_percentage'),
                    'srv_tbl_page' => $this->input->post('srv_tbl_page'),
                    'enable_srv_tbl_page' => $this->input->post('enable_srv_tbl_page') ? 1 : 0,
                    'srvs_tbl_bold' => $this->input->post('srvs_tbl_bold') ? 1 : 0,
                    'srvs_tbl_border' => $this->input->post('srvs_tbl_border') ? 1 : 0,
                    'srvs_tbl_arabic_numbers' => $this->input->post('srvs_tbl_arabic_numbers') ? 1 : 0,
                    'srv_tbl_paging_type' => $this->input->post('srv_tbl_paging_type'),
                    'srv_tbl_srv_align' => $this->input->post('srv_tbl_srv_align'),
                    'srv_tbl_srv_ar_h' => $this->input->post('srv_tbl_srv_ar_h'),
                    'srv_tbl_srv_en_h' => $this->input->post('srv_tbl_srv_en_h'),
                    'srv_tbl_cntr_align' => $this->input->post('srv_tbl_cntr_align'),
                    'srv_d_bg' => $this->input->post('srv_d_bg'),
                    'srv_tbl_cntr_ar_h' => $this->input->post('srv_tbl_cntr_ar_h'),
                    'srv_tbl_cntr_en_h' => $this->input->post('srv_tbl_cntr_en_h'),
                    'srv_tbl_cus_align' => $this->input->post('srv_tbl_cus_align'),
                    'srv_tbl_cus_ar_h' => $this->input->post('srv_tbl_cus_ar_h'),
                    'srv_tbl_cus_en_h' => $this->input->post('srv_tbl_cus_en_h'),
                    'srv_tbl_h_bg' => $this->input->post('srv_tbl_h_bg'),
                    'srv_tbl_h_font_color' => $this->input->post('srv_tbl_h_font_color'),
                    'srv_tbl_h_font_family' => $this->input->post('srv_tbl_h_font_family'),
                    'srv_tbl_h_font_size' => $this->input->post('srv_tbl_h_font_size'),
                    'srv_tbl_d_bg' => $this->input->post('srv_tbl_d_bg'),
                    'srv_tbl_d_font_color' => $this->input->post('srv_tbl_d_font_color'),
                    'srv_tbl_d_font_family' => $this->input->post('srv_tbl_d_font_family'),
                    'srv_tbl_d_font_size' => $this->input->post('srv_tbl_d_font_size'),
                );
                // Empty waiting area service table then fill in with new ones
                $this->db->delete('waiting_area_services_bar', ['waiting_area_id' => $id]);
                if (!empty($this->input->post('services_id'))) {
                    foreach ($this->input->post('services_id') as $srv) {
                        $srvs = ['waiting_area_id' => $id, 'service_id' => $srv];
                        $this->db->insert('waiting_area_services_bar', $srvs);
                    }
                }
                // Empty waiting area service table then fill in with new ones
                $this->db->delete('waiting_area_services_tbl', ['waiting_area_id' => $id]);
                if (!empty($this->input->post('srv_tbl_id'))) {
                    foreach ($this->input->post('srv_tbl_id') as $srv) {
                        $srvs = ['waiting_area_id' => $id, 'service_id' => $srv];
                        $this->db->insert('waiting_area_services_tbl', $srvs);
                    }
                }
                // Upload slider images
                // First check for empty fields
                for ($j = 1; $j <= $this->input->post('photos_count'); $j++) {
                    if (($_FILES['photo_' . $j]['name'] != '' && $this->input->post('priority_' . $j) == '') || ($_FILES['photo_' . $j]['name'] == '' && $this->input->post('priority_' . $j) != '')) {
                        $this->session->set_flashdata('error', 'Image and priority are required');
                        redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                    }
                }
                // Second check for existing priority
                for ($j = 1; $j <= $this->input->post('photos_count'); $j++) {
                    if ($_FILES['photo_' . $j]['name'] != '' && $this->input->post('priority_' . $j) != '') {
                        $priority = $this->input->post('priority_' . $j);
                        if (!is_numeric($priority) || $priority < 1) {
                            $this->session->set_flashdata('error', 'Numeric only allowed');
                            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                        }
                        $chk_priority = $this->db->get_where('slideshow_photos', [
                            'priority' => $priority, 'type' => 1, 'branch_id' => $branch_id, 'work_waiting_id' => $id])->num_rows();
                        if ($chk_priority > 0) {
                            $this->session->set_flashdata('error', 'Priority ' . $priority . ' already exists');
                            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                        }
                    }
                }
                for ($j = 1; $j <= $this->input->post('photos_count'); $j++) {
                    if ($_FILES['photo_' . $j]['name'] != '' && $this->input->post('priority_' . $j) != '') {
                        // Check for existing priority
                        $priority = $this->input->post('priority_' . $j);
                        if (!is_numeric($priority) || $priority < 1) {
                            $this->session->set_flashdata('error', 'Numeric only allowed');
                            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                        }
                        $chk_priority = $this->db->get_where('slideshow_photos', [
                            'priority' => $priority, 'type' => 1, 'branch_id' => $branch_id, 'work_waiting_id' => $id])->num_rows();
                        if ($chk_priority > 0) {
                            $this->session->set_flashdata('error', 'Priority ' . $priority . ' already exists');
                            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                        }
                        // Add to slider photos table
                        $data_photos = [
                            'type' => 1,
                            'work_waiting_id' => $id,
                            'branch_id' => $branch_id,
                            'priority' => $priority,
                        ];
                        $config['upload_path'] = './uploads/layouts/waiting_area';
                        $config['allowed_types'] = 'gif|jpg|png|jpeg';
                        $this->load->library('upload', $config);
                        if (!$this->upload->do_upload('photo_' . $j)) {
                            $this->session->set_flashdata('error', 'Select image');
                            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                        } else {
                            $data_photos['photo'] = $this->upload->data()['file_name'];
                        }
                        $this->db->insert('slideshow_photos', $data_photos);
                    }
                }
                // BEGIN FILE UPLOADS
                if ($_FILES['ws_logo']['name'] != '') {
                    $config['upload_path'] = './uploads/layouts/waiting_area';
                    $config['allowed_types'] = 'gif|jpg|png';
                    $this->load->library('upload', $config);
                    if (!$this->upload->do_upload('ws_logo')) {
                        $error = array('error' => $this->upload->display_errors());
                    } else {
                        $data['logo'] = $this->upload->data()['file_name'];
                    }
                }
                if ($_FILES['back_image']['name'] != '') {
                    $config['upload_path'] = './uploads/layouts/waiting_area';
                    $config['allowed_types'] = 'gif|jpg|png';
                    $this->load->library('upload', $config);
                    if (!$this->upload->do_upload('back_image')) {
                        $error = array('error' => $this->upload->display_errors());
                    } else {
                        $data['background_image'] = $this->upload->data()['file_name'];
                    }
                }
                if ($_FILES['separator_img_en']['name'] != '') {
                    $config['upload_path'] = './uploads/layouts/waiting_area';
                    $config['allowed_types'] = 'gif|jpg|png|jpeg';
                    $this->load->library('upload', $config);
                    if (!$this->upload->do_upload('separator_img_en')) {
                        $error = array('error' => $this->upload->display_errors());
                    } else {
                        $data['separator_img_en'] = $this->upload->data()['file_name'];
                    }
                }
                if ($_FILES['separator_img_ar']['name'] != '') {
                    $config['upload_path'] = './uploads/layouts/waiting_area';
                    $config['allowed_types'] = 'gif|jpg|png|jpeg';
                    $this->load->library('upload', $config);
                    if (!$this->upload->do_upload('separator_img_ar')) {
                        $error = array('error' => $this->upload->display_errors());
                    } else {
                        $data['separator_img_ar'] = $this->upload->data()['file_name'];
                    }
                }
                // Newsbar En
                if ($this->input->post('newsbar_select') == 0) {
                    // Rss
                    $url = $this->input->post('news_rss_en');
                    if (empty($url)) {
                        $this->session->set_flashdata('error', $this->lang->line('en_rss_link'));
                        redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                    }
                    $data['news_source_en'] = 0;
                    $data['news_rss_en'] = $url;
                } elseif ($this->input->post('newsbar_select') == 1) {
                    // Import from file
                    $data['news_source_en'] = 1;
                    $data['news_rss_en'] = null;
                    if (!empty($_FILES['file_uploaded_link']['name'])) {
                        $path = 'uploads/layouts/waiting_area/';
                        $config['upload_path'] = $path;
                        $config['allowed_types'] = 'xlsx|xls';
                        $config['remove_spaces'] = true;
                        $config['overwrite'] = true;
                        $this->load->library('upload', $config);
                        $this->upload->initialize($config);
                        if (!$this->upload->do_upload('file_uploaded_link')) {
                            $error = array('error' => $this->upload->display_errors());
                        } else {
                            $c_data = array('upload_data' => $this->upload->data());
                        }
                        if (!empty($c_data['upload_data']['file_name'])) {
                            $import_xls_file = $c_data['upload_data']['file_name'];
                        } else {
                            $import_xls_file = 0;
                        }
                        $inputFileName = $path . $import_xls_file;
                        try {
                            $pathinfo = pathinfo($_FILES["file_uploaded_link"]["name"]);
                            if (($pathinfo['extension'] == 'xlsx' || $pathinfo['extension'] == 'xls') && $_FILES['file_uploaded_link']['size'] > 0) {
                                $reader = ReaderEntityFactory::createReaderFromFile($inputFileName);
                                $reader->setShouldFormatDates(true);
                                $reader->open($inputFileName);
                                $newsbar = [];
                                foreach ($reader->getSheetIterator() as $sheet) {
                                    $i = 0;
                                    foreach ($sheet->getRowIterator() as $rowIndex => $row) {
                                        $i++;
                                        $rows = $row->toArray();
                                        $dt['description'] = $rows[0];
                                        $dt['w_a_id'] = $id;
                                        $dt['branch_id'] = $branch_id;
                                        $dt['type'] = 1;
                                        $newsbar[] = $dt;
                                    }
                                }
                                if ($newsbar) {
                                    $this->db->delete('newsbar_en', ['type' => 1, 'branch_id' => $branch_id, 'w_a_id' => $id]);
                                    $this->db->insert_batch('newsbar_en', $newsbar);
                                }
                                #Close excel file
                                $reader->close();
                            } else {
                                $this->session->set_flashdata('error', $this->lang->line('select_valid_excel'));
                                redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                            }
                        } catch (Exception $e) {
                            die('Error loading file "' . pathinfo($inputFileName, PATHINFO_BASENAME)
                                . '": ' . $e->getMessage());
                        }
                    }
                }
                // Newsbar Ar
                if ($this->input->post('newsbar_select_ar') == 0) {
                    // Rss
                    $url = $this->input->post('news_rss_ar');
                    if (empty($url)) {
                        $this->session->set_flashdata('error', $this->lang->line('ar_rss_link'));
                        redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                    }
                    $data['news_source_ar'] = 0;
                    $data['news_rss_ar'] = $url;
                } elseif ($this->input->post('newsbar_select_ar') == 1) {
                    // Import from file
                    $data['news_source_ar'] = 1;
                    $data['news_rss_ar'] = null;
                    if (!empty($_FILES['file_uploaded_link_ar']['name'])) {
                        $path = 'uploads/layouts/waiting_area/';
                        $config['upload_path'] = $path;
                        $config['allowed_types'] = 'xlsx|xls';
                        $config['remove_spaces'] = true;
                        $config['overwrite'] = true;
                        $this->load->library('upload', $config);
                        $this->upload->initialize($config);
                        if (!$this->upload->do_upload('file_uploaded_link_ar')) {
                            $error = array('error' => $this->upload->display_errors());
                        } else {
                            $c_data = array('upload_data' => $this->upload->data());
                        }
                        if (!empty($c_data['upload_data']['file_name'])) {
                            $import_xls_file = $c_data['upload_data']['file_name'];
                        } else {
                            $import_xls_file = 0;
                        }
                        $inputFileName = $path . $import_xls_file;
                        try {
                            $pathinfo = pathinfo($_FILES["file_uploaded_link_ar"]["name"]);
                            if (($pathinfo['extension'] == 'xlsx' || $pathinfo['extension'] == 'xls') && $_FILES['file_uploaded_link_ar']['size'] > 0) {
                                $reader = ReaderEntityFactory::createReaderFromFile($inputFileName);
                                $reader->setShouldFormatDates(true);
                                $reader->open($inputFileName);
                                $newsbar = [];
                                foreach ($reader->getSheetIterator() as $sheet) {
                                    $i = 0;
                                    foreach ($sheet->getRowIterator() as $rowIndex => $row) {
                                        $i++;
                                        $rows = $row->toArray();
                                        $dt['description'] = $rows[0];
                                        $dt['w_a_id'] = $id;
                                        $dt['branch_id'] = $branch_id;
                                        $dt['type'] = 1;
                                        $newsbar[] = $dt;
                                    }
                                }
                                if ($newsbar) {
                                    $this->db->delete('newsbar_ar', ['type' => 1, 'branch_id' => $branch_id, 'w_a_id' => $id]);
                                    $this->db->insert_batch('newsbar_ar', $newsbar);
                                }
                                #Close excel file
                                $reader->close();
                            } else {
                                $this->session->set_flashdata('error', $this->lang->line('select_valid_excel'));
                                redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                            }
                        } catch (Exception $e) {
                            die('Error loading file "' . pathinfo($inputFileName, PATHINFO_BASENAME)
                                . '": ' . $e->getMessage());
                        }
                    }
                }
                $photos_arr = [];
                $settings = $this->m_templates->get_waiting_area_sett_info($branch_id, $id);
                if (!empty($settings)) {
                    if (!empty($data['background_image']) && $settings->background_image != null) {
                        unlink('./uploads/layouts/waiting_area/' . $settings->background_image);
                    }
                    if (!empty($data['logo']) && $settings->logo != null) {
                        unlink('./uploads/layouts/waiting_area/' . $settings->logo);
                    }
                    if (!empty($data['movie_uploaded']) && $settings->movie_uploaded != null) {
                        unlink('./uploads/layouts/waiting_area/' . $settings->movie_uploaded);
                    }
                    $this->M_general->update('waiting_area_layout_sett', $settings->id, $data);
                    record_log('Update waiting area layout settings');
                } else {
                    $data['waiting_area_id'] = $id;
                    $this->M_general->insert('waiting_area_layout_sett', $data);
                    // $id = $this->db->insert_id();
                    record_log('Insert waiting area layout settings');
                }
                // first copy prev exists to new added waiting areas to copy settings or added now in prev upload step
                $prev_photos = $this->db->get_where('slideshow_photos', array('work_waiting_id' => $this->input->post('selected_waiting_area'), 'type' => 1))->result();
                foreach ($prev_photos as $photo) {
                    $copy_data = array('photo' => $photo->photo, 'type' => 1, 'work_waiting_id' => $id);
                    $exist_before = $this->db->get_where('slideshow_photos', $copy_data)->row();
                    if (!$exist_before) {
                        $this->db->insert('slideshow_photos', $copy_data);
                    }
                }
                // END
                if ($error != '') {
                    $this->session->set_flashdata('error', $error);
                    redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
                }
                update_branch_sett('waiting_area_layout_sett', $branch_id);
                redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id/success"));
            } else {
                $error['text'] = $this->lang->line('permission_denied');
                $this->load->view('private/page-500', $error);
            }
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function add_waiting_area_video_old($branch_id = null, $id = null)
    {
        if (empty($this->input->post('video_name'))) {
            $this->session->set_flashdata('error', 'Set video name');
            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
        }
        if ($_FILES['upload_movie']['name'] == '') {
            $this->session->set_flashdata('error', 'Select video');
            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
        }
        if (!is_numeric($this->input->post('video_priority')) || $this->input->post('video_priority') < 1) {
            $this->session->set_flashdata('error', 'Numeric only allowed');
            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
        }
        // Check for existing priority
        $chk_priority = $this->db->get_where('uploaded_videos', [
            'priority' => $this->input->post('video_priority'), 'type' => 1, 'branch_id' => $branch_id, 'w_a_id' => $id])->num_rows();
        if ($chk_priority > 0) {
            $this->session->set_flashdata('error', 'Priority already exists');
            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
        }
        $data = [
            'type' => 1,
            'branch_id' => $branch_id,
            'w_a_id' => $id,
            'name' => $this->input->post('video_name'),
            'priority' => $this->input->post('video_priority'),
        ];
        if ($_FILES['upload_movie']['name'] != '') {
            $configVideo['upload_path'] = './uploads/layouts/waiting_area/videos';
            $configVideo['allowed_types'] = 'mp3|mp4|wmv|avi|flv';
            $configVideo['max_size'] = '0';
            $configVideo['remove_spaces'] = true;
            $this->load->library('upload', $configVideo);
            if (!$this->upload->do_upload('upload_movie')) {
                $this->session->set_flashdata('error', 'Select video');
                redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
            } else {
                $data['video'] = $this->upload->data()['file_name'];
            }
        }
        $this->db->insert('uploaded_videos', $data);
        $this->session->set_flashdata('msg', $this->lang->line('data_submitted_successfully'));
        redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id/success"));
    }

    public function delete_waiting_area_video($id)
    {
        if ($this->session->userdata('login')) {
            $row = $this->db->get_where('uploaded_videos', ['id' => $id])->row();
            if ($row) {
                $branch_id = $row->branch_id;
                $w_a_id = $row->w_a_id;
                $this->db->delete('uploaded_videos', ['id' => $id]);
                record_log('Delete waiting area video');
            }
            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$w_a_id/success"));
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function add_waiting_area_slider($branch_id = null, $id = null)
    {
        if (empty($this->input->post('slider_name'))) {
            $this->session->set_flashdata('error', 'Set slider name');
            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
        }
        if ($_FILES['upload_slider']['name'] == '') {
            $this->session->set_flashdata('error', 'Select image');
            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
        }
        if (!is_numeric($this->input->post('slider_priority')) || $this->input->post('slider_priority') < 1) {
            $this->session->set_flashdata('error', 'Numeric only allowed');
            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
        }
        // Check for existing priority
        $chk_priority = $this->db->get_where('slideshow_photos', [
            'priority' => $this->input->post('slider_priority'), 'type' => 1, 'branch_id' => $branch_id, 'work_waiting_id' => $id])->num_rows();
        if ($chk_priority > 0) {
            $this->session->set_flashdata('error', 'Priority already exists');
            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
        }
        $data = [
            'type' => 1,
            'work_waiting_id' => $id,
            'branch_id' => $branch_id,
            'name' => $this->input->post('slider_name'),
            'priority' => $this->input->post('slider_priority'),
        ];
        if ($_FILES['upload_slider']['name'] != '') {
            $config['upload_path'] = './uploads/layouts/waiting_area';
            $config['allowed_types'] = 'gif|jpg|png|jpeg';
            $config['remove_spaces'] = true;
            $this->load->library('upload', $config);
            if (!$this->upload->do_upload('upload_slider')) {
                $this->session->set_flashdata('error', 'Select image');
                redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id"));
            } else {
                $data['photo'] = $this->upload->data()['file_name'];
            }
        }
        $this->db->insert('slideshow_photos', $data);
        $this->session->set_flashdata('msg', $this->lang->line('data_submitted_successfully'));
        redirect(base_url("C_templates/waiting_area_settings/$branch_id/$id/success"));
    }

    public function delete_waiting_area_slider($id)
    {
        if ($this->session->userdata('login')) {
            $row = $this->db->get_where('slideshow_photos', ['id' => $id])->row();
            if ($row) {
                $branch_id = $row->branch_id;
                $w_a_id = $row->work_waiting_id;
                $this->db->delete('slideshow_photos', ['id' => $id]);
                record_log('Delete waiting area slider');
            }
            redirect(base_url("C_templates/waiting_area_settings/$branch_id/$w_a_id/success"));
        } else {
            $error['text'] = $this->lang->line('permission_denied');
            $this->load->view('private/page-500', $error);
        }
    }

    public function add_waiting_area_video()
    {
        $results = ['status' => null, 'error' => null];
        $branch_id = $this->input->post('branch_id');
        $id = $this->input->post('w_a_id');
        $priority = $this->input->post('video_priority');
        if ($_FILES['upl_file']['name'] != '') {
            // Check for existing priority
            $chk_priority = $this->db->get_where('uploaded_videos', [
                'priority' => $priority, 'type' => 1, 'branch_id' => $branch_id, 'w_a_id' => $id])->num_rows();
            if ($chk_priority > 0) {
                $results['error'] = 'Priority already exists';
                $results['status'] = 'failed';
            } else {
                $this->load->library('pagination');
                $config['upload_path'] = './uploads/layouts/waiting_area/videos';
                $config['allowed_types'] = 'mp4|wmv|avi';
                $config['encrypt_name'] = true;
                $config['remove_spaces'] = true;
                $this->lang->load('upload');
                $this->load->library('upload', $config);
                $json = array();
                // Define file rules
                if (!$this->upload->do_upload('upl_file')) {
                    $results['error'] = $this->upload->display_errors();
                    $results['status'] = 'failed';
                } else {
                    $inserted_data = [
                        'type' => 1,
                        'branch_id' => $branch_id,
                        'w_a_id' => $id,
                        'priority' => $priority,
                    ];
                    $data = $this->upload->data();
                    $inserted_data['video'] = $data['file_name'];
                    $this->db->insert('uploaded_videos', $inserted_data);
                    $results['status'] = 'success';
                }
            }
        } else {
            $results['error'] = 'Select file';
            $results['status'] = 'failed';
        }
        header('Content-Type: application/json');
        echo json_encode($results);
    }

}
