<?php

defined('BASEPATH') or exit('No direct script access allowed');

use PhpOffice\PhpSpreadsheet\Shared\Date;

// functions will be called ...
// get_transactions / send_reservation_alert / set_terminal_free / send_danger_email

class Cron_job extends CI_Controller
{
    protected $service_id;
    protected $ticket_lang;
    protected $log_dir  = 'log/qedge/';
    protected $log_file = '';
    public function __construct()
    {
        parent::__construct();
    }

    private function search_name($input_name)
    {
        $this->db->select('id, key, name_s');
        $this->db->from('operator');
        $input_name = $this->db->escape_str($input_name);
        //$this->db->where("MATCH(name_s) AGAINST ('+$input_name' IN BOOLEAN MODE)", NULL, FALSE);
        $this->db->where('name_s', $input_name);
        $query = $this->db->get();
        return $query->row();
    }

    private function search_code($input_code)
    {
        $this->db->select('id, key, name_s');
        $this->db->from('operator');
        $input_code = $this->db->escape_str($input_code);
        $this->db->where('code', $input_code);
        $query = $this->db->get();
        return $query->row();
    }

    public function encrypt_openssl($string)
    {
        $key     = "&%#@?,:*";
        $ivArray = [0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF, 0x10, 0x32, 0x54, 0x76, 0x98, 0xBA, 0xDC, 0xFE];

        $iv = null;
        foreach ($ivArray as $element) {
            $iv .= CHR($element);
        }

        $pass_enc = $string;
        $block    = $this->openssl_cipher_block_length('AES-128-CBC');
        $pad      = $block - (strlen($pass_enc) % $block);
        $pass_enc .= str_repeat(chr($pad), $pad);

        $pass_enc = openssl_encrypt($pass_enc, 'AES-128-CBC', $key, OPENSSL_RAW_DATA, $iv);

        $pass_enc = base64_encode($pass_enc);
        return $pass_enc;
    }

    public function openssl_cipher_block_length($cipher)
    {
        $ivSize = @openssl_cipher_iv_length($cipher);

        // Invalid or unsupported cipher.
        if (false === $ivSize) {
            return false;
        }

        $iv = str_repeat("a", $ivSize);

        // Loop over possible block sizes, from 1 upto 1024 bytes.
        // The upper limit is arbitrary but high enough that is
        // sufficient for any current & foreseeable cipher.
        for ($size = 1; $size < 1024; $size++) {
            $output = openssl_encrypt(
                // Try varying the length of the raw data
                str_repeat("a", $size),
                // Cipher to use
                $cipher,
                // OpenSSL expands the key as necessary,
                // so this value is actually not relevant.
                "a",
                // Disable data padding: php_openssl will return false
                // if the input data's length is not a multiple
                // of the block size.
                //
                // We also pass OPENSSL_RAW_DATA so that PHP does not
                // base64-encode the data (since we just throw it away
                // afterwards anyway)
                OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING,
                // Feed it with an IV to avoid nasty warnings.
                // The actual value is not relevant as long as
                // it has the proper length.
                $iv
            );

            if (false !== $output) {
                return $size;
            }
        }

        // Could not determine the cipher's block length.
        return false;
    }

    public function import_reservation_excel()
    {
        ini_set("max_execution_time", 0);
        ini_set("memory_limit", "2048m");
        if ((check_is_server() || check_is_standalone())) {
            $customer_code_column_order        = 3;
            $customer_second_code_column_order = 2;
            $customer_name_column_order        = 6;
            $customer_second_name_column_order = 5;
            $service_code_column_order         = 10;
            $service_name_column_order         = 11;
            $agent_name_column_order           = 29;
            $agent_code_column_order           = 28;
            $date_column_order                 = 25;
            $time_column_order                 = 26;
            $confirmation_no                   = 1;
            $mobile_num_order                  = 7;
            $branch_id                         = 1;
            $target_file                       = './uploads/reservations/EXPORT.XLSX';
            $last_import_time                  = $this->db->select('last_import')->get_where('qreservation', ['id' => 1])->row();
            $file_time                         = date('Y-m-d H:i:s', filemtime($target_file));
            $cont                              = true;
            if (! $last_import_time->last_import || ($last_import_time->last_import < $file_time)) {
                $cont = true;
            }
            $nc          = [];
            $dd          = [];
            $agents_list = [];
            $agentss     = '';
            $branch_id   = 1;
            $role_id     = 23;
            $agentsMax   = $this->db->select('MAX(id) as max')->get('Operator')->row()->max;
            if ($cont) {
                $inputFileType = \PhpOffice\PhpSpreadsheet\IOFactory::identify($target_file);
                $objReader     = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);
                $objPHPExcel   = $objReader->load($target_file);
                $rows_count    = $objPHPExcel->setActiveSheetIndex(0)->getHighestRow();

                $letter = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB', 'AC'];
                $i      = 2;
                $val    = [];
                $ids    = [];
                $count  = 0;
                for ($i = 2; $i <= $rows_count; $i++) {
                    $confir_no     = $objPHPExcel->getActiveSheet()->getCell($letter[$confirmation_no - 1] . $i)->getValue();
                    $service_code  = $objPHPExcel->getActiveSheet()->getCell($letter[$service_code_column_order - 1] . $i)->getValue();
                    $service_name  = $objPHPExcel->getActiveSheet()->getCell($letter[$service_name_column_order - 1] . $i)->getValue();
                    $service_exist = $this->db->select('id')->where('service_code', $service_code)->get('service')->row();
                    if (! $service_exist && ! in_array($service_code, $nc)) {
                        $this->db->insert('servicecategories', ['CategoryName' => $service_name]);
                        $category_id = $this->db->insert_id();
                        if ($category_id) {
                            $this->db->insert('service', [
                                'service_name' => $service_name, 'arabic_service_name' => $service_name, 'cat_id' => $category_id, 'can_choose_agent' => 1, 'deleted' => 0, 'stop' => null, 'stop_period' => null, 'stop_entring_time' => null, 'escalation_time' => 30, 'kpi_avg_serving_time' => 10, 'warning_max_waiting_customers' => 0, 'warning_waiting_time' => '00:00:00', 'warning_serving_time' => '00:00:00', 'danger_max_waiting_customers' => 0, 'danger_waiting_time' => '00:10:00', 'danger_serving_time' => '00:00:00', 'service_code' => $service_code, 'enable_millensys' => 0, 'sound_muted' => 0,
                            ]);
                            $service_id = $this->db->insert_id();
                            if ($service_id) {
                                $service_exist = $this->db->select('id')->where('service_code', $service_code)->get('service')->row();
                            }
                        }
                        array_push($nc, $service_code);
                    }
                    $agent_name = $objPHPExcel->getActiveSheet()->getCell($letter[$agent_name_column_order - 1] . $i)->getValue();
                    $agent_code = $objPHPExcel->getActiveSheet()->getCell($letter[$agent_code_column_order - 1] . $i)->getValue();
                    if ($agent_code) {
                        $agent_exist = $this->search_code($agent_code);
                        if (! $agent_exist && ! in_array($agent_code, $dd)) {
                            array_push($dd, $agent_code);
                            $agents_list[] = ['name' => $agent_name, 'code' => $agent_code, 'service_id' => $service_exist->id, 'service_code' => $service_code, 'service_name' => $service_name];
                            $agentsMax++;
                            $agentsCount = $agentsMax;
                            $password    = $this->encrypt_openssl('Alfa123@123');
                            $agent_array = ['branchID' => $branch_id,
                                'name'                          => $agent_name, 'name_s' => $agent_name, 'code' => $agent_code, 'role' => $role_id, 'username' => 'Agent' . $agentsCount, 'password' => $password, 'email' => 'Agent' . $agentsCount . '@a.com', 'activate' => 1, 'password_resetted' => 1,
                            ];
                            $this->db->insert('Operator', $agent_array);
                            $id                  = $this->db->insert_id();
                            $agent_service_array = ['agent_id' => $id, 'service_id' => $service_exist->id];
                            $agent_service_exist = $this->db->select('id')->get_where('agent_services', $agent_service_array)->row();
                            if (! $agent_service_exist) {
                                $this->db->insert('agent_services', $agent_service_array);
                            }
                            $agentss .= $agent_code . ',';
                            $agent_exist = $this->search_code($agent_code);
                        }
                    }
                    $workflow_exist = $this->db->select('workflow_id')->get_where('workflow_steps', ['service_id' => $service_exist->id])->row();
                    if (! $workflow_exist) {
                        continue;
                    }
                    if ($date_column_order != '' && $time_column_order != '') {
                        $date_col = $objPHPExcel->getActiveSheet()->getCell($letter[$date_column_order - 1] . $i)->getValue();
                        if (is_numeric($date_col)) {
                            $date_col = Date::excelToDateTimeObject($date_col)->format('Y-m-d');
                        }
                        $time_col = $objPHPExcel->getActiveSheet()->getCell($letter[$time_column_order - 1] . $i)->getValue();
                        // Convert Excel serial time to PHP DateTime
                        $time = Date::excelToDateTimeObject($time_col);
                    }
                    $customer_code = '';
                    if ($customer_code_column_order != '') {
                        $customer_code = $objPHPExcel->getActiveSheet()->getCell($letter[$customer_code_column_order - 1] . $i)->getValue();
                        if (! $customer_code && $customer_second_code_column_order != '') {
                            $customer_code = $objPHPExcel->getActiveSheet()->getCell($letter[$customer_second_code_column_order - 1] . $i)->getValue();
                        }
                    }
                    $customer_name = '';
                    if ($customer_name_column_order != '') {
                        $customer_name = $objPHPExcel->getActiveSheet()->getCell($letter[$customer_name_column_order - 1] . $i)->getValue();
                        if (! $customer_name && $customer_second_name_column_order != '') {
                            $customer_name = $objPHPExcel->getActiveSheet()->getCell($letter[$customer_second_name_column_order - 1] . $i)->getValue();
                        }
                    }
                    if ($mobile_num_order != '') {
                        $mobile_num = $objPHPExcel->getActiveSheet()->getCell($letter[$mobile_num_order - 1] . $i)->getValue();
                    }
                    $customer = $this->db->get_where('customers', ['phone' => $mobile_num])->row();
                    if (! $customer) {
                        $this->db->insert('customers', ['name' => $customer_name, 'phone' => $mobile_num, 'segment_code' => 1, 'search_field_1' => $mobile_num, 'code' => $customer_code]);
                        $customer = $this->db->get_where('customers', ['phone' => $mobile_num])->row();
                    }
                    $workflow = $this->db->get_where('workflows', ['id' => $workflow_exist->workflow_id])->row();
                    if (! $customer || ! $workflow) {
                        continue;
                    }
                    $date_col = date("Y-m-d", strtotime($date_col));
                    $time_col = $time->format('H:i:s');

                    $interval_id = 0;

                    /*$timeslots = $this->db->get_where('timeslots', array('service_id' => $workflow->id, 'branch_id' => $branch_id,
								'date' => $date_col . ' 00:00:00', 'start_time <=' => $time_col))->result();
					$interval_id = 0;
					foreach ($timeslots as $timeslot) {
						$time = strtotime($timeslot->start_time);
						$endTime = date("H:i:s", strtotime("+" . $timeslot->duration . " minutes", $time));
						if ($endTime > $time_col) {
							$interval_id = $timeslot->id;
							break;
						}
					}
					if ($interval_id == 0) {
						//continue;
					}*/
                    $reservation_exist = $this->db->get_where('reservations', ['confirmation_no' => $confir_no])->row();

                    $reservation_array = ['customer_id' => $customer->id,
                        'agent_id'                               => $agent_exist ? $agent_exist->id : 0, 'agent_key' => $agent_exist ? $agent_exist->key : '', 'branch_id' => $branch_id, 'service_id' => $workflow->id, 'timeslot_id' => $interval_id,
                        'datetime'                               => $date_col . ' ' . $time_col, 'confirmation_no'   => $confir_no,
                        'account_num'                            => $customer_code, 'mobile_num'                     => $mobile_num, 'imported_from_file'                  => 1,
                    ];
                    if ($reservation_exist) {
                        $ids[] = $reservation_exist->id;
                        $this->db->where('id', $reservation_exist->id)->update('reservations', $reservation_array);
                        continue;
                    }
                    $this->db->insert('reservations', $reservation_array);
                    $ids[] = $this->db->insert_id();
                }
                $this->db->update('qreservation', ['last_import' => $file_time], ['id' => 1]);
                if ($ids) {
                    // Set the turn for each Service
                    $services = $this->db->distinct('service_id')->select('service_id')->where_in('id', $ids)->get('reservations')->result();
                    foreach ($services as $srv):
                        $reservations = $this->db->select('id')->where('service_id', $srv->service_id)->where_in('id', $ids)->get('reservations')->result();
                        $turn         = 0;
                        foreach ($reservations as $r):
                            $turn++;
                            $this->db->where('id', $r->id)->update('reservations', ['reservation_turn' => $turn]);
                        endforeach;
                    endforeach;
                }
                // Save agents
                if ($agents_list) {
                    $agent_missing_key = $this->db->where('key', '')->or_where('key', null)->get('Operator')->result();
                    foreach ($agent_missing_key as $item):
                        $key = get_key('Operator');
                        $this->db->where('id', $item->id)->update('Operator', ['key' => $key]);
                    endforeach;
                    $agent_services_missing_key = $this->db->select('agent_services.*,Operator.key')->join('Operator', 'Operator.id = agent_services.agent_id')->get('agent_services')->result();
                    foreach ($agent_services_missing_key as $item):
                        $this->db->where('id', $item->id)->update('agent_services', ['agent_key' => $item->key]);
                    endforeach;
                }
                var_export($nc);
                echo '<br/>';
                var_export($dd);
                echo '<br/>';
                echo $agentss;
                echo '<br/>';
                echo 'Imported Successfully';
            } else {
                echo 'Already Imported ' . $file_time;
            }
        } else {
            echo $this->lang->line('permission_denied');
        }
    }

    // if server ver call server_api/set_transactions with cron job to get transactions continously
    public function get_transactions()
    {
        if (check_is_server()) {
            redirect(base_url('Server_api/set_transactions'));
        }
    }

    public function send_reservation_alert()
    {
        $settings = $this->db->get('notification_content')->row();
        if (! $settings || $settings->before_reservation_with_mins == 0) {
            return;
        }
        $this->db->select('timeslots.date,timeslots.start_time,reservations.id,customers.phone,customers.email,reservations.confirmation_no');
        $this->db->join('customers', 'customers.id=reservations.customer_id');
        $this->db->join('timeslots', 'timeslots.id=reservations.timeslot_id');
        $comming_reservation = $this->db->get_where('reservations', ['timeslots.date >=' => date('Y-m-d 00:00:00'),
            'timeslots.start_time >='                                                             => date('H:i:s'),
            'before_alert_sent'                                                                   => 0])->result();
        foreach ($comming_reservation as $reserv) {
            $date         = date('Y-m-d', strtotime($reserv->date));
            $minutes      = $settings->before_reservation_with_mins;
            $to_time      = strtotime($date . ' ' . $reserv->start_time);
            $from_time    = strtotime(date('Y-m-d H:i:s'));
            $actuall_mins = round(abs($to_time - $from_time) / 60, 2);
            if ($actuall_mins <= $minutes) {
                $this->Crud_model->send_customer_notifications($reserv->phone, null, 'before_reservation'); // reservation code = null to send sms only not email was :$reserv->confirmation_no
                $this->db->update('reservations', ['before_alert_sent' => 1], ['id' => $reserv->id]);
            }
        }
    }

    public function set_terminal_free() // if agent forget to logout
    {
        $settings = $this->db->get('OrganisationGlobalSettings')->row();
        if ($settings && $settings->max_shift_hours != 0) {
            $open_sessions  = $this->db->get_where('sessions', ['logout_time' => null])->result();
            $open_terminals = $this->db->get_where('terminal', ['status' => 1])->result();
            foreach ($open_sessions as $session) {
                $this->db->update('sessions', ['logout_time' => date('Y-m-d H:i:s')], ['id' => $session->id]);
                $this->db->update('terminal', ['status' => 0], ['id' => $session->terminalID]);
                /*$shift_max_end_time = date("Y-m-d H:i:s", strtotime('+' . $settings->max_shift_hours . ' hours', strtotime($session->login_time)));
                if ($shift_max_end_time <= date('Y-m-d H:i:s')) {
                    
                }*/
            }
            foreach ($open_terminals as $terminal) {
                $this->db->update('terminal', ['status' => 0, 'idle_state' => 0], ['id' => $terminal->id]);
            }
        }
    }

    public function send_danger_email()
    {
        $error_msg_per_service     = '';
        $error_msg_waiting         = '';
        $error_msg_per_service_arr = [];
        $data                      = $this->check_alarms();
        for ($i = 0; $i < 2; $i++) {
            if (! empty($data[$i])) {
                for ($j = 0; $j < count($data[$i]); $j++) {
                    if ($i == 0) {
                        $error_msg_waiting .= $data[$i][$j][0] . '<br/>';
                    } else {
                        $error_msg_per_service .= $data[$i][$j][0] . '<br/>';
                        if (! isset($error_msg_per_service_arr[$data[$i][$j][2]])) {
                            $error_msg_per_service_arr[$data[$i][$j][2]] = '';
                        }
                        $error_msg_per_service_arr[$data[$i][$j][2]] .= $data[$i][$j][0] . '<br/>';
                    }
                }
            }
        }
        foreach ($error_msg_per_service_arr as $key => $value) {
            $this->Crud_model->send_manager_notifcations('waiting_cuatomer_exceed_acceptable_limit', $value, $key);
        }
        // to Head office
        if ($error_msg_waiting != '') {
            $this->Crud_model->send_manager_notifcations('waiting_cuatomer_per_branch', $error_msg_waiting);
        }
        if ($error_msg_per_service != '') {
            $this->Crud_model->send_manager_notifcations('waiting_cuatomer_exceed_acceptable_limit_ho', $error_msg_per_service);
        }
    }

    public function check_alarms()
    {
        $this->load->model('m_alarm');
        $waiting_customers             = $this->m_alarm->check_waiting_customers();
        $waiting_customers_per_service = $this->m_alarm->check_waiting_customers_per_service();
        return [$waiting_customers, $waiting_customers_per_service]; //, $idle_alarm, $waiting_time, $serving_time
    }

    public function test()
    {
        $this->Crud_model->send_email('<EMAIL>', 'test', 'testing ....');
    }

    public function deactivate_accounts()
    {
        $settings = $this->db->get('OrganisationGlobalSettings')->row();
        if ($settings && $settings->deactivate_period != 0) {
            // Users
            $this->db->select('MAX(date(login_time)) as login_time,userID');
            $this->db->group_by('userID');
            $users = $this->db->get_where('sessions', ['user_type' => 'User'])->result();
            foreach ($users as $user) {
                $now        = new DateTime(date('Y-m-d'));
                $last_login = new DateTime($user->login_time);

                $diff = $now->diff($last_login)->format("%a");
                if ($diff >= $settings->deactivate_period) {
                    $this->db->update('Users', ['activate' => 0], ['UserID' => $user->userID]);
                }
            }
            // Agents
            $this->db->select('MAX(date(login_time)) as login_time,userID');
            $this->db->group_by('userID');
            $agents = $this->db->get_where('sessions', ['user_type' => 'Agent'])->result();
            foreach ($agents as $user) {
                $now        = new DateTime(date('Y-m-d'));
                $last_login = new DateTime($user->login_time);
                $diff       = $now->diff($last_login)->format("%a");
                if ($diff >= $settings->deactivate_period) {
                    $this->db->update('Operator', ['activate' => 0], ['id' => $user->userID]);
                }
            }
        }
    }

    public function reset_today_tickets()
    {
        $this->db->select('BranchID');
        $branches = $this->db->get_where('Branch', ['deleted' => 0])->result();
        foreach ($branches as $branch) {
            $this->Crud_model->clear_tickets($branch->BranchID);
        }
    }

    public function set_transactions_deleted()
    {
        $date = date('Y-m-d');
        $this->db->select('*');
        $this->db->from('transactions');
        $this->db->where('DATE(date) <', $date);
        $this->db->group_start();
        $this->db->group_start();
        $this->db->where('status', 0);
        $this->db->group_end();
        $this->db->or_group_start();
        $this->db->where('status', 1);
        $this->db->group_end();
        $this->db->or_group_start();
        $this->db->where('status', 5);
        $this->db->group_end();
        $this->db->group_end();
        $transactions = $this->db->get()->result();
        if (! empty($transactions)) {
            foreach ($transactions as $t) {
                $this->db->where('id', $t->id);
                $this->db->update('transactions', ['its_turn' => 0, 'status' => 2, 'end_serving' => date('Y-m-d H:i:s')]);
            }
        }
    }

    public function reset_terminals()
    {
        $date = date('Y-m-d');
        $this->db->distinct("terminalID");
        $this->db->select('id,terminalID');
        $this->db->from('sessions');
        $this->db->where('DATE(login_time) <', $date);
        $this->db->where('logout_time is null');
        $this->db->where('terminalID is not null');
        $terminals = $this->db->get()->result();
        if (! empty($terminals)) {
            foreach ($terminals as $t) {
                $this->db->where('id', $t->terminalID);
                $this->db->update('terminal', ['idle_state' => 0, 'status' => 0, 'status_change_from' => date('Y-m-d H:i:s')]);
                $this->db->where('id', $t->id);
                $this->db->update('sessions', ['logout_time' => date('Y-m-d H:i:s')]);
            }
        }
    }

    public function scan_hardware($host = null, $org = "WAM2", $port = 1883)
    {
        set_time_limit(0);
        $this->log_file = $this->log_dir . '/' . $org . '.txt';
        /* LOG File Directory Initialize */
        if (! file_exists($this->log_dir)) {
            mkdir($this->log_dir, 0775, true);
        }
        $settings = $this->db->get('OrganisationGlobalSettings')->row();
        if ($host && $port && $org) {
            $server    = $host;    // change if necessary
            $mqtt_port = $port;    // change if necessary
            $username  = '';       // set your username
            $password  = '';       // set your password
            $client_id = uniqid(); // make sure this is unique for connecting to sever - you could use uniqid()
            $this->load->library("phpMQTT", [$server, $mqtt_port, $client_id, null]);
            $mqtt             = $this->phpmqtt;
            $this->service_id = null;
            if ($mqtt->connect(true, null, $username, $password)) {
                $action = $this->input->get("action");
                if ($action == 'Config') {
                    $mqtt->publish($org . '/Command', 'Config= on', 0);
                    $page = $_SERVER['PHP_SELF'] . '?action=Scan';
                    echo 'Config ON';
                    echo '<br/>';
                    echo '<a href="' . $page . '">Scan Hardware</a>';
                } else if ($action == 'Scan') {
                    $mqtt->publish($org . '/Command', 'HW_Scan= on', 0);
                    $page = $_SERVER['PHP_SELF'] . '?action=Result';
                    echo 'Scan ON';
                    header("Refresh: 3; url=$page");
                } elseif ($action == 'Result') {
                    $msg = $mqtt->subscribeAndWaitForMessage($org . '/HW_Units', 0);
                    if ($msg) {
                        write_file(FCPATH . $this->log_file, PHP_EOL . '[' . date('Y-m-d H:i:s') . '][' . $org . '/HW_Units' . '] -> Response: ' . $msg . PHP_EOL, 'a');
                        $page = $_SERVER['PHP_SELF'] . '?action=Result';
                        $sec  = "5";
                        header("Refresh: $sec; url=$page");
                    }
                } else {
                    write_file(FCPATH . $this->log_file, '');
                    $page = $_SERVER['PHP_SELF'] . '?action=Config';
                    echo '<a href="' . $page . '">Config ON</a>';
                }
                $mqtt->close();
            } else {
                echo "Time out!\n";
            }
        }
    }

    public function mqtt_fetch_multi($host = null, $port = null, $org = "WAM2", $CH = "CH2_From_GW")
    {
        set_time_limit(0);
        $this->today    = date('Ymd');
        $this->log_file = $this->log_dir . '/' . $this->today . '.txt';
        /* LOG File Directory Initialize */
        if (! file_exists($this->log_dir)) {
            mkdir($this->log_dir, 0775, true);
        }
        if (! file_exists($this->log_file)) {
            mkdir($this->log_dir, 0775, true);
            write_file(FCPATH . $this->log_file, '');
        }
        $settings = $this->db->get('OrganisationGlobalSettings')->row();
        if (($settings && ! empty($settings->mqtt_broker_ip)) || $host) {
            $enable_audio = ! empty($settings->enable_audio_api) && ! empty($settings->audio_api_url) ? true : false;
            $audio_api    = $enable_audio ? $settings->audio_api_url : null;
            $server       = $host ? $host : $settings->mqtt_broker_ip; // change if necessary
            $mqtt_port    = $port ? $port : 1883;                      // change if necessary
            $username     = '';                                        // set your username
            $password     = '';                                        // set your password
            $client_id    = uniqid();                                  // make sure this is unique for connecting to sever - you could use uniqid()
            $this->load->library("phpMQTT", [$server, $mqtt_port, $client_id, null]);
            $mqtt             = $this->phpmqtt;
            $this->service_id = null;
            if ($mqtt->connect(true, null, $username, $password)) {
                $msg = $mqtt->subscribeAndWaitForMessage($org . '/' . $CH, 0);
                if ($msg) {
                    $decrypt = $this->decrypt_mqtt($msg);
                    write_file(FCPATH . $this->log_file, PHP_EOL . '[' . date('Y-m-d H:i:s') . '][' . $org . '/' . $CH . '] -> Request: ' . $msg . PHP_EOL, 'a');
                    var_export($decrypt);
                    if (! empty($decrypt['type']) && $decrypt['type'] == 'T' && ! empty($decrypt['op_code'])) {
                        $unitID       = (int) $decrypt['unit'];
                        $terminalInfo = $this->db->get_where('terminal', ['mqtt_ch' => $CH, 'mqtt_terminal_id' => $unitID])->row();
                        if ($terminalInfo) {
                            $terminalId = (int) $terminalInfo->display_address;

                            $mqttInfo = ! empty($terminalInfo->mqtt) ? unserialize($terminalInfo->mqtt) : [];

                            $defaultCH    = ! empty($mqttInfo['channel']) ? $mqttInfo['channel'] : null;
                            $defaultTOPIC = ! empty($mqttInfo['topic']) ? $mqttInfo['topic'] : null;

                            // Assign Client Display Paramters
                            $cdisplayTYPE  = ! empty($mqttInfo['cdtype']) ? $mqttInfo['cdtype'] : null;
                            $cdisplayID    = ! empty($mqttInfo['cid']) ? $mqttInfo['cid'] : null;
                            $cdisplayCH    = ! empty($mqttInfo['cch']) ? $mqttInfo['cch'] : $defaultCH;
                            $cdisplayTOPIC = ! empty($mqttInfo['ctopic']) ? $mqttInfo['ctopic'] : $defaultTOPIC;

                            // Assign Service Display Paramters
                            $sdisplayTYPE  = ! empty($mqttInfo['sdtype']) ? $mqttInfo['sdtype'] : null;
                            $sdisplayID    = ! empty($mqttInfo['sid']) ? $mqttInfo['sid'] : null;
                            $sdisplayCH    = ! empty($mqttInfo['sch']) ? $mqttInfo['sch'] : $defaultCH;
                            $sdisplayTOPIC = ! empty($mqttInfo['stopic']) ? $mqttInfo['stopic'] : $defaultTOPIC;

                            // Assign Audio Player Paramters
                            $aplayerLANG  = ! empty($mqttInfo['alang']) ? $mqttInfo['alang'] : 'E';
                            $aplayerID    = ! empty($mqttInfo['aid']) ? $mqttInfo['aid'] : null;
                            $aplayerCH    = ! empty($mqttInfo['ach']) ? $mqttInfo['ach'] : $defaultCH;
                            $aplayerTOPIC = ! empty($mqttInfo['atopic']) ? $mqttInfo['atopic'] : $defaultTOPIC;

                            $mqtt_file = read_file(FCPATH . 'mqtt_' . $terminalId . '.txt');

                            $data = json_decode($mqtt_file);
                            switch ($decrypt['op_code']):
                        // Agent login
                        case 37:
                            $agent    = $this->db->get_where('operator', ['num' => $decrypt['data'], 'activate' => 1])->row();
                            $terminal = $agent ? $this->db->get_where('terminal', ['display_address' => $terminalId, 'branch_id' => $agent->branchID])->row() : null;
                            if ($terminal && $agent) {
                                    $agent->terminal_id  = $terminalId;
                                    $agent->window_no    = $terminal->window_no;
                                    $agent->window_no_id = $terminal->id;
                                    // Write terminal info with agent login to file
                                    write_file(FCPATH . '/mqtt_' . $terminalId . '.txt', json_encode($agent));
                                    $resp = '$0T0' . $unitID . ',13,done*6A';
                                    $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                    // Log response
                                    write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    usleep(300000);
                                    //$mqtt->publish($org . '/' .  $dsbCH, '$0D0' . $terminalId . ',' . ($displayType == 2 ? 13 : 11) . ',0000*6A', 0);
                            } else {
                                write_file(FCPATH . '/mqtt.txt', '');
                                $resp = '$0T0' . $unitID . ',13,erro*60';
                                $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                // Log response
                                write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                            }
                            break;
                        // Agent Logout
                        case 38:
                            write_file(FCPATH . '/mqtt_' . $terminalId . '.txt', '');
                            $resp = '$0T0' . $unitID . ',13,off*6A';
                            $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                            // Log response
                            write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                            if ($cdisplayTYPE && $cdisplayID && $cdisplayCH && $cdisplayTOPIC) {
                                usleep(300000);
                                $resp = '$0D0' . $cdisplayID . ',' . $cdisplayTYPE . ',' . ($cdisplayTYPE == 11 ? '0000' : 'off') . '*6A';
                                $mqtt->publish($cdisplayTOPIC . '/' . $cdisplayCH, $resp, 0);
                                // Log response
                                write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $cdisplayTOPIC . '/' . $cdisplayCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                            }
                            break;
                        // Next Call
                        case 28:
                            if (! empty($data->branchID)) {
                                $ticket = $this->serve_ticket($data->branchID, $data->terminal_id, $data->id, $data->key);
                                $resp   = '$0T0' . $unitID . ',' . ($ticket ? 11 : 13) . ',' . ($ticket ? $ticket : 'end') . '*6A';
                                $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                // Log response
                                write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                if ($ticket) {
                                    $service_audio = false;
                                    $service_info  = $this->db->select('sound_muted')->get_where('service', ['id' => $this->service_id])->row();
                                    if ($service_info && ! $service_info->sound_muted) {
                                        $service_audio = true;
                                    }
                                    if ($cdisplayTYPE && $cdisplayID && $cdisplayCH && $cdisplayTOPIC) {
                                        usleep(300000);
                                        $resp = '$0D0' . $cdisplayID . ',' . $cdisplayTYPE . ',' . $ticket . '*6A';
                                        $mqtt->publish($cdisplayTOPIC . '/' . $cdisplayCH, $resp, 0);
                                        // Log response
                                        write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $cdisplayTOPIC . '/' . $cdisplayCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    }
                                    if ($sdisplayTYPE && $sdisplayID && $sdisplayCH && $sdisplayTOPIC) {
                                        usleep(300000);
                                        $resp = '$0S0' . $sdisplayID . ',' . $sdisplayTYPE . ',' . ($ticket . '-' . $data->window_no) . '*6A';
                                        $mqtt->publish($sdisplayTOPIC . '/' . $sdisplayCH, $resp, 0);
                                        // Log response
                                        write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $sdisplayTOPIC . '/' . $sdisplayCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    }
                                    if ($service_audio && $aplayerID && $aplayerCH && $aplayerTOPIC) {
                                        usleep(300000);
                                        $resp = '$0A0' . $aplayerID . ',46,' . ($this->ticket_lang ? ucfirst(substr($this->ticket_lang, 0, 1)) : $aplayerLANG) . ($ticket . '-' . $data->window_no) . '*6A';
                                        $mqtt->publish($aplayerTOPIC . '/' . $aplayerCH, $resp, 0);
                                        // Log response
                                        write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $aplayerTOPIC . '/' . $aplayerCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    }
                                    if ($audio_api && $this->service_id && $service_audio) {
                                        $post_data = [
                                            'windowNumber'  => $data->window_no,
                                            'serviceNumber' => $this->service_id,
                                            'clientNumber'  => $ticket,
                                        ];
                                        connect_to_remote($audio_api, $post_data);
                                        usleep(300000);
                                    }
                                }
                            } else {
                                $resp = '$0T0' . $unitID . ',13,erro*60';
                                $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                // Log response
                                write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                            }
                            break;
                        // Specific customer
                        case 29:
                            if (! empty($data->branchID)) {
                                $ticket = $this->serve_ticket($data->branchID, $data->terminal_id, $data->id, $data->key, $decrypt['data']);
                                if ($ticket) {
                                    $service_audio = false;
                                    $service_info  = $this->db->select('sound_muted')->get_where('service', ['id' => $this->service_id])->row();
                                    if ($service_info && ! $service_info->sound_muted) {
                                        $service_audio = true;
                                    }
                                    $resp = '$0T0' . $unitID . ',11,' . $ticket . '*6A';
                                    $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                    // Log response
                                    write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    if ($cdisplayTYPE && $cdisplayID && $cdisplayCH && $cdisplayTOPIC) {
                                        usleep(300000);
                                        $resp = '$0D0' . $cdisplayID . ',' . $cdisplayTYPE . ',' . $ticket . '*6A';
                                        $mqtt->publish($cdisplayTOPIC . '/' . $cdisplayCH, $resp, 0);
                                        // Log response
                                        write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $cdisplayTOPIC . '/' . $cdisplayCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    }
                                    if ($sdisplayTYPE && $sdisplayID && $sdisplayCH && $sdisplayTOPIC) {
                                        usleep(300000);
                                        $resp = '$0S0' . $sdisplayID . ',' . $sdisplayTYPE . ',' . ($ticket . '-' . $data->window_no) . '*6A';
                                        $mqtt->publish($sdisplayTOPIC . '/' . $sdisplayCH, $resp, 0);
                                        // Log response
                                        write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $sdisplayTOPIC . '/' . $sdisplayCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    }
                                    if ($service_audio && $aplayerID && $aplayerCH && $aplayerTOPIC) {
                                        usleep(300000);
                                        $resp = '$0A0' . $aplayerID . ',46,' . ($this->ticket_lang ? ucfirst(substr($this->ticket_lang, 0, 1)) : $aplayerLANG) . ($ticket . '-' . $data->window_no) . '*6A';
                                        $mqtt->publish($aplayerTOPIC . '/' . $aplayerCH, $resp, 0);
                                        // Log response
                                        write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $aplayerTOPIC . '/' . $aplayerCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    }
                                    if ($audio_api && $this->service_id && $service_audio) {
                                        $post_data = [
                                            'windowNumber'  => $data->window_no,
                                            'serviceNumber' => $this->service_id,
                                            'clientNumber'  => $ticket,
                                        ];
                                        connect_to_remote($audio_api, $post_data);
                                        usleep(300000);
                                    }
                                } else {
                                    $ticket = $decrypt['data'];
                                    $resp   = '$0T0' . $unitID . ',11,' . $ticket . '*6A';
                                    $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                    // Log response
                                    write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    if ($cdisplayTYPE && $cdisplayID && $cdisplayCH && $cdisplayTOPIC) {
                                        usleep(300000);
                                        $resp = '$0D0' . $cdisplayID . ',' . $cdisplayTYPE . ',' . $ticket . '*6A';
                                        $mqtt->publish($cdisplayTOPIC . '/' . $cdisplayCH, $resp, 0);
                                        // Log response
                                        write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $cdisplayTOPIC . '/' . $cdisplayCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    }
                                    if ($sdisplayTYPE && $sdisplayID && $sdisplayCH && $sdisplayTOPIC) {
                                        usleep(300000);
                                        $resp = '$0S0' . $sdisplayID . ',' . $sdisplayTYPE . ',' . ($ticket . '-' . $data->window_no) . '*6A';
                                        $mqtt->publish($sdisplayTOPIC . '/' . $sdisplayCH, $resp, 0);
                                        // Log response
                                        write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $sdisplayTOPIC . '/' . $sdisplayCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    }

                                    if ($aplayerID && $aplayerCH && $aplayerTOPIC && $aplayerLANG) {
                                        usleep(300000);
                                        $resp = '$0A0' . $aplayerID . ',46,' . $aplayerLANG . ($ticket . '-' . $data->window_no) . '*6A';
                                        $mqtt->publish($aplayerTOPIC . '/' . $aplayerCH, $resp, 0);
                                        // Log response
                                        write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $aplayerTOPIC . '/' . $aplayerCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                    }

                                    if ($audio_api && $this->service_id) {
                                        $post_data = [
                                            'windowNumber'  => $data->window_no,
                                            'serviceNumber' => $this->service_id,
                                            'clientNumber'  => $ticket,
                                        ];
                                        connect_to_remote($audio_api, $post_data);
                                        usleep(300000);
                                    }
                                }
                            } else {
                                $resp = '$0T0' . $unitID . ',13,erro*60';
                                $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                // Log response
                                write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                            }
                            break;
                        // Transfer customer
                        case 30:
                            if (! empty($data->branchID)) {
                                $this->load->model('M_terminal');
                                $serving_ticket = $this->M_terminal->get_serving_ticket($data->branchID, $data->terminal_id);
                                if ($serving_ticket) {
                                    $ticket_num = $this->M_terminal->Transfer_ticket($serving_ticket->id);
                                    record_log('Transfer Current Ticket with num.:' . $ticket_num);
                                    $w_t_id     = $serving_ticket->work_transaction_id;
                                    $ticket_num = $serving_ticket->ticket_num;
                                    $msg        = 'Transfer Current Ticket with num: ' . $ticket_num . ' at ' . date('Y-m-d H:i:s');
                                    row_data_log($w_t_id, $serving_ticket->id, $ticket_num, $msg);
                                    $resp = '$0T0' . $unitID . ',13,don*6A';
                                    $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                    // Log response
                                    write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                } else {
                                    $resp = '$0T0' . $unitID . ',13,erro*60';
                                    $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                    // Log response
                                    write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                }
                            } else {
                                $resp = '$0T0' . $unitID . ',13,erro*60';
                                $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                // Log response
                                write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                            }
                            break;
                        // Hold customer
                        case 32:
                            if (! empty($data->branchID)) {
                                $this->load->model('M_terminal');
                                $serving_ticket = $this->M_terminal->get_serving_ticket($data->branchID, $data->terminal_id);
                                if ($serving_ticket) {
                                    $ticket_num = $this->M_terminal->Hold_ticket($serving_ticket->id);
                                    record_log('Hold Current Ticket with num.:' . $ticket_num);
                                    $w_t_id     = $serving_ticket->work_transaction_id;
                                    $ticket_num = $serving_ticket->ticket_num;
                                    $msg        = 'Hold Current Ticket with num: ' . $ticket_num . ' at ' . date('Y-m-d H:i:s');
                                    row_data_log($w_t_id, $serving_ticket->id, $ticket_num, $msg);
                                    $resp = '$0T0' . $unitID . ',13,don*6A';
                                    $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                    // Log response
                                    write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                } else {
                                    $resp = '$0T0' . $unitID . ',13,erro*60';
                                    $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                    // Log response
                                    write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                }
                            } else {
                                $resp = '$0T0' . $unitID . ',13,erro*60';
                                $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                // Log response
                                write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                            }
                            break;
                        // Call Holded customer
                        case 33:
                            if (! empty($data->branchID)) {
                                $this->load->model('M_terminal');
                                $holding_tickets = $this->M_terminal->get_holding_transactions($data->branchID, $data->window_no_id, $data->window_no);
                                if ($holding_tickets) {
                                    foreach ($holding_tickets as $t):
                                        $resp = '$0T0' . $unitID . ',11,' . $t->ticket_num . '*6A';
                                        $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                        // Log response
                                        write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                        sleep(2);
                                    endforeach;
                                } else {
                                    $resp = '$0T0' . $unitID . ',13,erro*60';
                                    $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                    // Log response
                                    write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                                }
                            } else {
                                $resp = '$0T0' . $unitID . ',13,erro*60';
                                $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                // Log response
                                write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                            }
                            break;
                        // Number waiting customers
                        case 36:
                            if (! empty($data->branchID)) {
                                $count = $this->count_tickets($data->branchID);
                                $resp  = '$0T0' . $unitID . ',' . ($count ? 11 : 13) . ',' . ($count ? $count : 'end') . '*6A';
                                $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                // Log response
                                write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                            } else {
                                $resp = '$0T0' . $unitID . ',13,erro*60';
                                $mqtt->publish($defaultTOPIC . '/' . $defaultCH, $resp, 0);
                                // Log response
                                write_file(FCPATH . $this->log_file, '[' . date('Y-m-d H:i:s') . '][' . $defaultTOPIC . '/' . $defaultCH . '] -> Response: ' . $resp . PHP_EOL, 'a');
                            }
                            break;
                            endswitch;
                        }
                    }
                }
                $mqtt->close();
                $page = $_SERVER['PHP_SELF'];
                $sec  = "1";
                header("Refresh: $sec; url=$page");
            } else {
                echo "Time out!\n";
            }
        }
    }

    public function decrypt_mqtt($msg)
    {
        $array    = explode(',', $msg); // Example $1T02,37,2*6f
        $array[2] = str_replace('*', '_', $array[2]);
        $dt       = explode('_', $array[2]);
        $data     = $dt[0];
        $checksum = $dt[1];
        $return   = ['type' => substr($array[0], 2, 1), 'unit' => preg_replace("/[^0-9\s]/", "", substr($array[0], 3)), 'op_code' => (int) $array[1], 'data' => preg_replace("/[^0-9\s]/", "", $data), 'checksum' => $checksum];
        return $return;
    }

    public function count_tickets($branch)
    {
        return $this->db->select('count(id) as waiting_count')->get_where('transactions', ['branch_id' => $branch, 'status' => 0, 'its_turn' => 1])->row()->waiting_count;
    }

    public function serve_ticket($branch, $terminal, $agent, $agent_key, $ticket_num = null)
    {
        $this->load->model('M_terminal');
        $this->load->library('config_writer');
        $serving_ticket = $this->M_terminal->get_serving_ticket($branch, $terminal);
        if ($serving_ticket) {
            $this->M_terminal->close_current_ticket($serving_ticket->id);
        }
        $writer           = $this->config_writer->get_instance();
        $terminal_id      = "current_serve_ticket_" . $terminal;
        $t_num            = '';
        $transaction_id   = '';
        $services_num_arr = [];
        $branch_terminal  = $this->M_terminal->get_branch_terminal($branch, $terminal);
        if ($branch_terminal) {
            $agent_has_services = $this->M_terminal->get_agent_services($agent_key);
            foreach ($agent_has_services as $service) {
                if (! in_array($service->id, $services_num_arr)) {
                    array_push($services_num_arr, $service->id);
                }
            }
            // get from transactions
            $calling_mechanism   = terminal_calling_technique($branch_terminal);
            $waiting_transaction = $this->M_terminal->get_waiting_transactions($services_num_arr, $calling_mechanism, true, $branch, $terminal, $agent, $ticket_num);
            if ($waiting_transaction) {
                $this->service_id  = $this->M_terminal->serve_ticket($waiting_transaction->id, $terminal, $agent, $agent_key);
                $t_num             = $waiting_transaction ? $waiting_transaction->ticket_num : '';
                $this->ticket_lang = $waiting_transaction ? $waiting_transaction->language : '';
                $transaction_id    = $waiting_transaction ? $waiting_transaction->id : '';
                $w_t_id            = $waiting_transaction ? $waiting_transaction->work_transaction_id : '';
                $writer->write($terminal_id, $waiting_transaction->id);
            }
        }
        if ($serving_ticket) {
            record_log('Close Current Ticket with num. ' . $serving_ticket->ticket_num . ' and start serve the Next One with num. ' . $t_num);
            $s_w_t_id         = $serving_ticket->work_transaction_id;
            $s_ticket_num     = $serving_ticket->ticket_num;
            $s_transaction_id = $serving_ticket->id;
            $msg              = 'Close Current Ticket with num: ' . $s_ticket_num . ' at ' . date('Y-m-d H:i:s');
            row_data_log($s_w_t_id, $s_transaction_id, $s_ticket_num, $msg);
        }
        if ($t_num) {
            record_log('Start serving the Next ticket with number:' . $t_num);
            $msg = 'Start serving the Next ticket with number: ' . $t_num . ' at ' . date('Y-m-d H:i:s');
            row_data_log($w_t_id, $transaction_id, $t_num, $msg);
            return preg_replace("/[^0-9\s]/", "", $t_num);
        } else {
            return null;
        }
    }

}
